"""
    pygments.lexers.matlab
    ~~~~~~~~~~~~~~~~~~~~~~

    Lexers for Matlab and related languages.

    :copyright: Copyright 2006-2025 by the Pygments team, see AUTHORS.
    :license: BSD, see LICENSE for details.
"""

import re

from pygments.lexer import Lex<PERSON>, RegexLexer, bygroups, default, words, \
    do_insertions, include
from pygments.token import Text, Comment, Operator, Keyword, Name, String, \
    Number, Punctuation, Generic, Whitespace

from pygments.lexers import _scilab_builtins

__all__ = ['MatlabLexer', 'MatlabSessionLexer', 'OctaveLexer', 'ScilabLexer']


class MatlabLexer(RegexLexer):
    """
    For Matlab source code.
    """
    name = 'Matlab'
    aliases = ['matlab']
    filenames = ['*.m']
    mimetypes = ['text/matlab']
    url = 'https://www.mathworks.com/products/matlab.html'
    version_added = '0.10'

    _operators = r'-|==|~=|<=|>=|<|>|&&|&|~|\|\|?|\.\*|\*|\+|\.\^|\^|\.\\|\./|/|\\'

    tokens = {
        'expressions': [
            # operators:
            (_operators, Operator),

            # numbers (must come before punctuation to handle `.5`; cannot use
            # `\b` due to e.g. `5. + .5`).  The negative lookahead on operators
            # avoids including the dot in `1./x` (the dot is part of `./`).
            (rf'(?<!\w)((\d+\.\d+)|(\d*\.\d+)|(\d+\.(?!{_operators})))'
             r'([eEf][+-]?\d+)?(?!\w)', Number.Float),
            (r'\b\d+[eEf][+-]?[0-9]+\b', Number.Float),
            (r'\b\d+\b', Number.Integer),

            # punctuation:
            (r'\[|\]|\(|\)|\{|\}|:|@|\.|,', Punctuation),
            (r'=|:|;', Punctuation),

            # quote can be transpose, instead of string:
            # (not great, but handles common cases...)
            (r'(?<=[\w)\].])\'+', Operator),

            (r'"(""|[^"])*"', String),

            (r'(?<![\w)\].])\'', String, 'string'),
            (r'[a-zA-Z_]\w*', Name),
            (r'\s+', Whitespace),
            (r'.', Text),
        ],
        'root': [
            # line starting with '!' is sent as a system command.  not sure what
            # label to use...
            (r'^!.*', String.Other),
            (r'%\{\s*\n', Comment.Multiline, 'blockcomment'),
            (r'%.*$', Comment),
            (r'(\s*^\s*)(function)\b', bygroups(Whitespace, Keyword), 'deffunc'),
            (r'(\s*^\s*)(properties)(\s+)(\()',
             bygroups(Whitespace, Keyword, Whitespace, Punctuation),
             ('defprops', 'propattrs')),
            (r'(\s*^\s*)(properties)\b',
             bygroups(Whitespace, Keyword), 'defprops'),

            # from 'iskeyword' on version 9.4 (R2018a):
            # Check that there is no preceding dot, as keywords are valid field
            # names.
            (words(('break', 'case', 'catch', 'classdef', 'continue',
                    'dynamicprops', 'else', 'elseif', 'end', 'for', 'function',
                    'global', 'if', 'methods', 'otherwise', 'parfor',
                    'persistent', 'return', 'spmd', 'switch',
                    'try', 'while'),
                   prefix=r'(?<!\.)(\s*)(', suffix=r')\b'),
             bygroups(Whitespace, Keyword)),

            (
                words(
                    [
                        # See https://mathworks.com/help/matlab/referencelist.html
                        # Below data from 2021-02-10T18:24:08Z
                        # for Matlab release R2020b
                        "BeginInvoke",
                        "COM",
                        "Combine",
                        "CombinedDatastore",
                        "EndInvoke",
                        "Execute",
                        "FactoryGroup",
                        "FactorySetting",
                        "Feval",
                        "FunctionTestCase",
                        "GetCharArray",
                        "GetFullMatrix",
                        "GetVariable",
                        "GetWorkspaceData",
                        "GraphPlot",
                        "H5.close",
                        "H5.garbage_collect",
                        "H5.get_libversion",
                        "H5.open",
                        "H5.set_free_list_limits",
                        "H5A.close",
                        "H5A.create",
                        "H5A.delete",
                        "H5A.get_info",
                        "H5A.get_name",
                        "H5A.get_space",
                        "H5A.get_type",
                        "H5A.iterate",
                        "H5A.open",
                        "H5A.open_by_idx",
                        "H5A.open_by_name",
                        "H5A.read",
                        "H5A.write",
                        "H5D.close",
                        "H5D.create",
                        "H5D.get_access_plist",
                        "H5D.get_create_plist",
                        "H5D.get_offset",
                        "H5D.get_space",
                        "H5D.get_space_status",
                        "H5D.get_storage_size",
                        "H5D.get_type",
                        "H5D.open",
                        "H5D.read",
                        "H5D.set_extent",
                        "H5D.vlen_get_buf_size",
                        "H5D.write",
                        "H5DS.attach_scale",
                        "H5DS.detach_scale",
                        "H5DS.get_label",
                        "H5DS.get_num_scales",
                        "H5DS.get_scale_name",
                        "H5DS.is_scale",
                        "H5DS.iterate_scales",
                        "H5DS.set_label",
                        "H5DS.set_scale",
                        "H5E.clear",
                        "H5E.get_major",
                        "H5E.get_minor",
                        "H5E.walk",
                        "H5F.close",
                        "H5F.create",
                        "H5F.flush",
                        "H5F.get_access_plist",
                        "H5F.get_create_plist",
                        "H5F.get_filesize",
                        "H5F.get_freespace",
                        "H5F.get_info",
                        "H5F.get_mdc_config",
                        "H5F.get_mdc_hit_rate",
                        "H5F.get_mdc_size",
                        "H5F.get_name",
                        "H5F.get_obj_count",
                        "H5F.get_obj_ids",
                        "H5F.is_hdf5",
                        "H5F.mount",
                        "H5F.open",
                        "H5F.reopen",
                        "H5F.set_mdc_config",
                        "H5F.unmount",
                        "H5G.close",
                        "H5G.create",
                        "H5G.get_info",
                        "H5G.open",
                        "H5I.dec_ref",
                        "H5I.get_file_id",
                        "H5I.get_name",
                        "H5I.get_ref",
                        "H5I.get_type",
                        "H5I.inc_ref",
                        "H5I.is_valid",
                        "H5L.copy",
                        "H5L.create_external",
                        "H5L.create_hard",
                        "H5L.create_soft",
                        "H5L.delete",
                        "H5L.exists",
                        "H5L.get_info",
                        "H5L.get_name_by_idx",
                        "H5L.get_val",
                        "H5L.iterate",
                        "H5L.iterate_by_name",
                        "H5L.move",
                        "H5L.visit",
                        "H5L.visit_by_name",
                        "H5ML.compare_values",
                        "H5ML.get_constant_names",
                        "H5ML.get_constant_value",
                        "H5ML.get_function_names",
                        "H5ML.get_mem_datatype",
                        "H5O.close",
                        "H5O.copy",
                        "H5O.get_comment",
                        "H5O.get_comment_by_name",
                        "H5O.get_info",
                        "H5O.link",
                        "H5O.open",
                        "H5O.open_by_idx",
                        "H5O.set_comment",
                        "H5O.set_comment_by_name",
                        "H5O.visit",
                        "H5O.visit_by_name",
                        "H5P.all_filters_avail",
                        "H5P.close",
                        "H5P.close_class",
                        "H5P.copy",
                        "H5P.create",
                        "H5P.equal",
                        "H5P.exist",
                        "H5P.fill_value_defined",
                        "H5P.get",
                        "H5P.get_alignment",
                        "H5P.get_alloc_time",
                        "H5P.get_attr_creation_order",
                        "H5P.get_attr_phase_change",
                        "H5P.get_btree_ratios",
                        "H5P.get_char_encoding",
                        "H5P.get_chunk",
                        "H5P.get_chunk_cache",
                        "H5P.get_class",
                        "H5P.get_class_name",
                        "H5P.get_class_parent",
                        "H5P.get_copy_object",
                        "H5P.get_create_intermediate_group",
                        "H5P.get_driver",
                        "H5P.get_edc_check",
                        "H5P.get_external",
                        "H5P.get_external_count",
                        "H5P.get_family_offset",
                        "H5P.get_fapl_core",
                        "H5P.get_fapl_family",
                        "H5P.get_fapl_multi",
                        "H5P.get_fclose_degree",
                        "H5P.get_fill_time",
                        "H5P.get_fill_value",
                        "H5P.get_filter",
                        "H5P.get_filter_by_id",
                        "H5P.get_gc_references",
                        "H5P.get_hyper_vector_size",
                        "H5P.get_istore_k",
                        "H5P.get_layout",
                        "H5P.get_libver_bounds",
                        "H5P.get_link_creation_order",
                        "H5P.get_link_phase_change",
                        "H5P.get_mdc_config",
                        "H5P.get_meta_block_size",
                        "H5P.get_multi_type",
                        "H5P.get_nfilters",
                        "H5P.get_nprops",
                        "H5P.get_sieve_buf_size",
                        "H5P.get_size",
                        "H5P.get_sizes",
                        "H5P.get_small_data_block_size",
                        "H5P.get_sym_k",
                        "H5P.get_userblock",
                        "H5P.get_version",
                        "H5P.isa_class",
                        "H5P.iterate",
                        "H5P.modify_filter",
                        "H5P.remove_filter",
                        "H5P.set",
                        "H5P.set_alignment",
                        "H5P.set_alloc_time",
                        "H5P.set_attr_creation_order",
                        "H5P.set_attr_phase_change",
                        "H5P.set_btree_ratios",
                        "H5P.set_char_encoding",
                        "H5P.set_chunk",
                        "H5P.set_chunk_cache",
                        "H5P.set_copy_object",
                        "H5P.set_create_intermediate_group",
                        "H5P.set_deflate",
                        "H5P.set_edc_check",
                        "H5P.set_external",
                        "H5P.set_family_offset",
                        "H5P.set_fapl_core",
                        "H5P.set_fapl_family",
                        "H5P.set_fapl_log",
                        "H5P.set_fapl_multi",
                        "H5P.set_fapl_sec2",
                        "H5P.set_fapl_split",
                        "H5P.set_fapl_stdio",
                        "H5P.set_fclose_degree",
                        "H5P.set_fill_time",
                        "H5P.set_fill_value",
                        "H5P.set_filter",
                        "H5P.set_fletcher32",
                        "H5P.set_gc_references",
                        "H5P.set_hyper_vector_size",
                        "H5P.set_istore_k",
                        "H5P.set_layout",
                        "H5P.set_libver_bounds",
                        "H5P.set_link_creation_order",
                        "H5P.set_link_phase_change",
                        "H5P.set_mdc_config",
                        "H5P.set_meta_block_size",
                        "H5P.set_multi_type",
                        "H5P.set_nbit",
                        "H5P.set_scaleoffset",
                        "H5P.set_shuffle",
                        "H5P.set_sieve_buf_size",
                        "H5P.set_sizes",
                        "H5P.set_small_data_block_size",
                        "H5P.set_sym_k",
                        "H5P.set_userblock",
                        "H5R.create",
                        "H5R.dereference",
                        "H5R.get_name",
                        "H5R.get_obj_type",
                        "H5R.get_region",
                        "H5S.close",
                        "H5S.copy",
                        "H5S.create",
                        "H5S.create_simple",
                        "H5S.extent_copy",
                        "H5S.get_select_bounds",
                        "H5S.get_select_elem_npoints",
                        "H5S.get_select_elem_pointlist",
                        "H5S.get_select_hyper_blocklist",
                        "H5S.get_select_hyper_nblocks",
                        "H5S.get_select_npoints",
                        "H5S.get_select_type",
                        "H5S.get_simple_extent_dims",
                        "H5S.get_simple_extent_ndims",
                        "H5S.get_simple_extent_npoints",
                        "H5S.get_simple_extent_type",
                        "H5S.is_simple",
                        "H5S.offset_simple",
                        "H5S.select_all",
                        "H5S.select_elements",
                        "H5S.select_hyperslab",
                        "H5S.select_none",
                        "H5S.select_valid",
                        "H5S.set_extent_none",
                        "H5S.set_extent_simple",
                        "H5T.array_create",
                        "H5T.close",
                        "H5T.commit",
                        "H5T.committed",
                        "H5T.copy",
                        "H5T.create",
                        "H5T.detect_class",
                        "H5T.enum_create",
                        "H5T.enum_insert",
                        "H5T.enum_nameof",
                        "H5T.enum_valueof",
                        "H5T.equal",
                        "H5T.get_array_dims",
                        "H5T.get_array_ndims",
                        "H5T.get_class",
                        "H5T.get_create_plist",
                        "H5T.get_cset",
                        "H5T.get_ebias",
                        "H5T.get_fields",
                        "H5T.get_inpad",
                        "H5T.get_member_class",
                        "H5T.get_member_index",
                        "H5T.get_member_name",
                        "H5T.get_member_offset",
                        "H5T.get_member_type",
                        "H5T.get_member_value",
                        "H5T.get_native_type",
                        "H5T.get_nmembers",
                        "H5T.get_norm",
                        "H5T.get_offset",
                        "H5T.get_order",
                        "H5T.get_pad",
                        "H5T.get_precision",
                        "H5T.get_sign",
                        "H5T.get_size",
                        "H5T.get_strpad",
                        "H5T.get_super",
                        "H5T.get_tag",
                        "H5T.insert",
                        "H5T.is_variable_str",
                        "H5T.lock",
                        "H5T.open",
                        "H5T.pack",
                        "H5T.set_cset",
                        "H5T.set_ebias",
                        "H5T.set_fields",
                        "H5T.set_inpad",
                        "H5T.set_norm",
                        "H5T.set_offset",
                        "H5T.set_order",
                        "H5T.set_pad",
                        "H5T.set_precision",
                        "H5T.set_sign",
                        "H5T.set_size",
                        "H5T.set_strpad",
                        "H5T.set_tag",
                        "H5T.vlen_create",
                        "H5Z.filter_avail",
                        "H5Z.get_filter_info",
                        "Inf",
                        "KeyValueDatastore",
                        "KeyValueStore",
                        "MException",
                        "MException.last",
                        "MaximizeCommandWindow",
                        "MemoizedFunction",
                        "MinimizeCommandWindow",
                        "NET",
                        "NET.Assembly",
                        "NET.GenericClass",
                        "NET.NetException",
                        "NET.addAssembly",
                        "NET.convertArray",
                        "NET.createArray",
                        "NET.createGeneric",
                        "NET.disableAutoRelease",
                        "NET.enableAutoRelease",
                        "NET.invokeGenericMethod",
                        "NET.isNETSupported",
                        "NET.setStaticProperty",
                        "NaN",
                        "NaT",
                        "OperationResult",
                        "PutCharArray",
                        "PutFullMatrix",
                        "PutWorkspaceData",
                        "PythonEnvironment",
                        "Quit",
                        "RandStream",
                        "ReleaseCompatibilityException",
                        "ReleaseCompatibilityResults",
                        "Remove",
                        "RemoveAll",
                        "Setting",
                        "SettingsGroup",
                        "TallDatastore",
                        "Test",
                        "TestResult",
                        "Tiff",
                        "TransformedDatastore",
                        "ValueIterator",
                        "VersionResults",
                        "VideoReader",
                        "VideoWriter",
                        "abs",
                        "accumarray",
                        "acos",
                        "acosd",
                        "acosh",
                        "acot",
                        "acotd",
                        "acoth",
                        "acsc",
                        "acscd",
                        "acsch",
                        "actxGetRunningServer",
                        "actxserver",
                        "add",
                        "addCause",
                        "addCorrection",
                        "addFile",
                        "addFolderIncludingChildFiles",
                        "addGroup",
                        "addLabel",
                        "addPath",
                        "addReference",
                        "addSetting",
                        "addShortcut",
                        "addShutdownFile",
                        "addStartupFile",
                        "addStyle",
                        "addToolbarExplorationButtons",
                        "addboundary",
                        "addcats",
                        "addedge",
                        "addevent",
                        "addlistener",
                        "addmulti",
                        "addnode",
                        "addpath",
                        "addpoints",
                        "addpref",
                        "addprop",
                        "addsample",
                        "addsampletocollection",
                        "addtodate",
                        "addts",
                        "addvars",
                        "adjacency",
                        "airy",
                        "align",
                        "alim",
                        "all",
                        "allchild",
                        "alpha",
                        "alphaShape",
                        "alphaSpectrum",
                        "alphaTriangulation",
                        "alphamap",
                        "alphanumericBoundary",
                        "alphanumericsPattern",
                        "amd",
                        "analyzeCodeCompatibility",
                        "ancestor",
                        "angle",
                        "animatedline",
                        "annotation",
                        "ans",
                        "any",
                        "appdesigner",
                        "append",
                        "area",
                        "arguments",
                        "array2table",
                        "array2timetable",
                        "arrayDatastore",
                        "arrayfun",
                        "asFewOfPattern",
                        "asManyOfPattern",
                        "ascii",
                        "asec",
                        "asecd",
                        "asech",
                        "asin",
                        "asind",
                        "asinh",
                        "assert",
                        "assignin",
                        "atan",
                        "atan2",
                        "atan2d",
                        "atand",
                        "atanh",
                        "audiodevinfo",
                        "audiodevreset",
                        "audioinfo",
                        "audioplayer",
                        "audioread",
                        "audiorecorder",
                        "audiowrite",
                        "autumn",
                        "axes",
                        "axis",
                        "axtoolbar",
                        "axtoolbarbtn",
                        "balance",
                        "bandwidth",
                        "bar",
                        "bar3",
                        "bar3h",
                        "barh",
                        "barycentricToCartesian",
                        "base2dec",
                        "batchStartupOptionUsed",
                        "bctree",
                        "beep",
                        "bench",
                        "besselh",
                        "besseli",
                        "besselj",
                        "besselk",
                        "bessely",
                        "beta",
                        "betainc",
                        "betaincinv",
                        "betaln",
                        "between",
                        "bfsearch",
                        "bicg",
                        "bicgstab",
                        "bicgstabl",
                        "biconncomp",
                        "bin2dec",
                        "binary",
                        "binscatter",
                        "bitand",
                        "bitcmp",
                        "bitget",
                        "bitnot",
                        "bitor",
                        "bitset",
                        "bitshift",
                        "bitxor",
                        "blanks",
                        "ble",
                        "blelist",
                        "blkdiag",
                        "bluetooth",
                        "bluetoothlist",
                        "bone",
                        "boundary",
                        "boundaryFacets",
                        "boundaryshape",
                        "boundingbox",
                        "bounds",
                        "box",
                        "boxchart",
                        "brighten",
                        "brush",
                        "bsxfun",
                        "bubblechart",
                        "bubblechart3",
                        "bubblelegend",
                        "bubblelim",
                        "bubblesize",
                        "builddocsearchdb",
                        "builtin",
                        "bvp4c",
                        "bvp5c",
                        "bvpget",
                        "bvpinit",
                        "bvpset",
                        "bvpxtend",
                        "caldays",
                        "caldiff",
                        "calendar",
                        "calendarDuration",
                        "calllib",
                        "calmonths",
                        "calquarters",
                        "calweeks",
                        "calyears",
                        "camdolly",
                        "cameratoolbar",
                        "camlight",
                        "camlookat",
                        "camorbit",
                        "campan",
                        "campos",
                        "camproj",
                        "camroll",
                        "camtarget",
                        "camup",
                        "camva",
                        "camzoom",
                        "canUseGPU",
                        "canUseParallelPool",
                        "cart2pol",
                        "cart2sph",
                        "cartesianToBarycentric",
                        "caseInsensitivePattern",
                        "caseSensitivePattern",
                        "cast",
                        "cat",
                        "categorical",
                        "categories",
                        "caxis",
                        "cd",
                        "cdf2rdf",
                        "cdfepoch",
                        "cdfinfo",
                        "cdflib",
                        "cdfread",
                        "ceil",
                        "cell",
                        "cell2mat",
                        "cell2struct",
                        "cell2table",
                        "celldisp",
                        "cellfun",
                        "cellplot",
                        "cellstr",
                        "centrality",
                        "centroid",
                        "cgs",
                        "char",
                        "characterListPattern",
                        "characteristic",
                        "checkcode",
                        "chol",
                        "cholupdate",
                        "choose",
                        "chooseContextMenu",
                        "circshift",
                        "circumcenter",
                        "cla",
                        "clabel",
                        "class",
                        "classUnderlying",
                        "clc",
                        "clear",
                        "clearAllMemoizedCaches",
                        "clearPersonalValue",
                        "clearTemporaryValue",
                        "clearpoints",
                        "clearvars",
                        "clf",
                        "clibArray",
                        "clibConvertArray",
                        "clibIsNull",
                        "clibIsReadOnly",
                        "clibRelease",
                        "clibgen.buildInterface",
                        "clibgen.generateLibraryDefinition",
                        "clipboard",
                        "clock",
                        "clone",
                        "close",
                        "closeFile",
                        "closereq",
                        "cmap2gray",
                        "cmpermute",
                        "cmunique",
                        "codeCompatibilityReport",
                        "colamd",
                        "collapse",
                        "colon",
                        "colorbar",
                        "colorcube",
                        "colormap",
                        "colororder",
                        "colperm",
                        "com.mathworks.engine.MatlabEngine",
                        "com.mathworks.matlab.types.CellStr",
                        "com.mathworks.matlab.types.Complex",
                        "com.mathworks.matlab.types.HandleObject",
                        "com.mathworks.matlab.types.Struct",
                        "combine",
                        "comet",
                        "comet3",
                        "compan",
                        "compass",
                        "complex",
                        "compose",
                        "computer",
                        "comserver",
                        "cond",
                        "condeig",
                        "condensation",
                        "condest",
                        "coneplot",
                        "configureCallback",
                        "configureTerminator",
                        "conj",
                        "conncomp",
                        "containers.Map",
                        "contains",
                        "containsrange",
                        "contour",
                        "contour3",
                        "contourc",
                        "contourf",
                        "contourslice",
                        "contrast",
                        "conv",
                        "conv2",
                        "convertCharsToStrings",
                        "convertContainedStringsToChars",
                        "convertStringsToChars",
                        "convertTo",
                        "convertvars",
                        "convexHull",
                        "convhull",
                        "convhulln",
                        "convn",
                        "cool",
                        "copper",
                        "copyHDU",
                        "copyfile",
                        "copygraphics",
                        "copyobj",
                        "corrcoef",
                        "cos",
                        "cosd",
                        "cosh",
                        "cospi",
                        "cot",
                        "cotd",
                        "coth",
                        "count",
                        "countcats",
                        "cov",
                        "cplxpair",
                        "cputime",
                        "createCategory",
                        "createFile",
                        "createImg",
                        "createLabel",
                        "createTbl",
                        "criticalAlpha",
                        "cross",
                        "csc",
                        "cscd",
                        "csch",
                        "ctranspose",
                        "cummax",
                        "cummin",
                        "cumprod",
                        "cumsum",
                        "cumtrapz",
                        "curl",
                        "currentProject",
                        "cylinder",
                        "daspect",
                        "dataTipInteraction",
                        "dataTipTextRow",
                        "datacursormode",
                        "datastore",
                        "datatip",
                        "date",
                        "datenum",
                        "dateshift",
                        "datestr",
                        "datetick",
                        "datetime",
                        "datevec",
                        "day",
                        "days",
                        "dbclear",
                        "dbcont",
                        "dbdown",
                        "dbmex",
                        "dbquit",
                        "dbstack",
                        "dbstatus",
                        "dbstep",
                        "dbstop",
                        "dbtype",
                        "dbup",
                        "dde23",
                        "ddeget",
                        "ddensd",
                        "ddesd",
                        "ddeset",
                        "deblank",
                        "dec2base",
                        "dec2bin",
                        "dec2hex",
                        "decic",
                        "decomposition",
                        "deconv",
                        "deg2rad",
                        "degree",
                        "del2",
                        "delaunay",
                        "delaunayTriangulation",
                        "delaunayn",
                        "delete",
                        "deleteCol",
                        "deleteFile",
                        "deleteHDU",
                        "deleteKey",
                        "deleteRecord",
                        "deleteRows",
                        "delevent",
                        "delimitedTextImportOptions",
                        "delsample",
                        "delsamplefromcollection",
                        "demo",
                        "descriptor",
                        "det",
                        "details",
                        "detectImportOptions",
                        "detrend",
                        "deval",
                        "dfsearch",
                        "diag",
                        "dialog",
                        "diary",
                        "diff",
                        "diffuse",
                        "digitBoundary",
                        "digitsPattern",
                        "digraph",
                        "dir",
                        "disableDefaultInteractivity",
                        "discretize",
                        "disp",
                        "display",
                        "dissect",
                        "distances",
                        "dither",
                        "divergence",
                        "dmperm",
                        "doc",
                        "docsearch",
                        "dos",
                        "dot",
                        "double",
                        "drag",
                        "dragrect",
                        "drawnow",
                        "dsearchn",
                        "duration",
                        "dynamicprops",
                        "echo",
                        "echodemo",
                        "echotcpip",
                        "edgeAttachments",
                        "edgecount",
                        "edges",
                        "edit",
                        "eig",
                        "eigs",
                        "ellipj",
                        "ellipke",
                        "ellipsoid",
                        "empty",
                        "enableDefaultInteractivity",
                        "enableLegacyExplorationModes",
                        "enableNETfromNetworkDrive",
                        "enableservice",
                        "endsWith",
                        "enumeration",
                        "eomday",
                        "eps",
                        "eq",
                        "equilibrate",
                        "erase",
                        "eraseBetween",
                        "erf",
                        "erfc",
                        "erfcinv",
                        "erfcx",
                        "erfinv",
                        "error",
                        "errorbar",
                        "errordlg",
                        "etime",
                        "etree",
                        "etreeplot",
                        "eval",
                        "evalc",
                        "evalin",
                        "event.ClassInstanceEvent",
                        "event.DynamicPropertyEvent",
                        "event.EventData",
                        "event.PropertyEvent",
                        "event.hasListener",
                        "event.listener",
                        "event.proplistener",
                        "eventlisteners",
                        "events",
                        "exceltime",
                        "exist",
                        "exit",
                        "exp",
                        "expand",
                        "expint",
                        "expm",
                        "expm1",
                        "export",
                        "export2wsdlg",
                        "exportapp",
                        "exportgraphics",
                        "exportsetupdlg",
                        "extract",
                        "extractAfter",
                        "extractBefore",
                        "extractBetween",
                        "eye",
                        "ezpolar",
                        "faceNormal",
                        "factor",
                        "factorial",
                        "false",
                        "fclose",
                        "fcontour",
                        "feather",
                        "featureEdges",
                        "feof",
                        "ferror",
                        "feval",
                        "fewerbins",
                        "fft",
                        "fft2",
                        "fftn",
                        "fftshift",
                        "fftw",
                        "fgetl",
                        "fgets",
                        "fieldnames",
                        "figure",
                        "figurepalette",
                        "fileDatastore",
                        "fileMode",
                        "fileName",
                        "fileattrib",
                        "filemarker",
                        "fileparts",
                        "fileread",
                        "filesep",
                        "fill",
                        "fill3",
                        "fillmissing",
                        "filloutliers",
                        "filter",
                        "filter2",
                        "fimplicit",
                        "fimplicit3",
                        "find",
                        "findCategory",
                        "findEvent",
                        "findFile",
                        "findLabel",
                        "findall",
                        "findedge",
                        "findfigs",
                        "findgroups",
                        "findnode",
                        "findobj",
                        "findprop",
                        "finish",
                        "fitsdisp",
                        "fitsinfo",
                        "fitsread",
                        "fitswrite",
                        "fix",
                        "fixedWidthImportOptions",
                        "flag",
                        "flintmax",
                        "flip",
                        "flipedge",
                        "fliplr",
                        "flipud",
                        "floor",
                        "flow",
                        "flush",
                        "fmesh",
                        "fminbnd",
                        "fminsearch",
                        "fopen",
                        "format",
                        "fplot",
                        "fplot3",
                        "fprintf",
                        "frame2im",
                        "fread",
                        "freeBoundary",
                        "freqspace",
                        "frewind",
                        "fscanf",
                        "fseek",
                        "fsurf",
                        "ftell",
                        "ftp",
                        "full",
                        "fullfile",
                        "func2str",
                        "function_handle",
                        "functions",
                        "functiontests",
                        "funm",
                        "fwrite",
                        "fzero",
                        "gallery",
                        "gamma",
                        "gammainc",
                        "gammaincinv",
                        "gammaln",
                        "gather",
                        "gca",
                        "gcbf",
                        "gcbo",
                        "gcd",
                        "gcf",
                        "gcmr",
                        "gco",
                        "genpath",
                        "geoaxes",
                        "geobasemap",
                        "geobubble",
                        "geodensityplot",
                        "geolimits",
                        "geoplot",
                        "geoscatter",
                        "geotickformat",
                        "get",
                        "getAColParms",
                        "getAxes",
                        "getBColParms",
                        "getColName",
                        "getColType",
                        "getColorbar",
                        "getConstantValue",
                        "getEqColType",
                        "getFileFormats",
                        "getHDUnum",
                        "getHDUtype",
                        "getHdrSpace",
                        "getImgSize",
                        "getImgType",
                        "getLayout",
                        "getLegend",
                        "getMockHistory",
                        "getNumCols",
                        "getNumHDUs",
                        "getNumInputs",
                        "getNumInputsImpl",
                        "getNumOutputs",
                        "getNumOutputsImpl",
                        "getNumRows",
                        "getOpenFiles",
                        "getProfiles",
                        "getPropertyGroupsImpl",
                        "getReport",
                        "getTimeStr",
                        "getVersion",
                        "getabstime",
                        "getappdata",
                        "getaudiodata",
                        "getdatasamples",
                        "getdatasamplesize",
                        "getenv",
                        "getfield",
                        "getframe",
                        "getinterpmethod",
                        "getnext",
                        "getpinstatus",
                        "getpixelposition",
                        "getplayer",
                        "getpoints",
                        "getpref",
                        "getqualitydesc",
                        "getrangefromclass",
                        "getsamples",
                        "getsampleusingtime",
                        "gettimeseriesnames",
                        "gettsafteratevent",
                        "gettsafterevent",
                        "gettsatevent",
                        "gettsbeforeatevent",
                        "gettsbeforeevent",
                        "gettsbetweenevents",
                        "getvaropts",
                        "ginput",
                        "gmres",
                        "gobjects",
                        "gplot",
                        "grabcode",
                        "gradient",
                        "graph",
                        "gray",
                        "grid",
                        "griddata",
                        "griddatan",
                        "griddedInterpolant",
                        "groot",
                        "groupcounts",
                        "groupfilter",
                        "groupsummary",
                        "grouptransform",
                        "gsvd",
                        "gtext",
                        "guidata",
                        "guide",
                        "guihandles",
                        "gunzip",
                        "gzip",
                        "h5create",
                        "h5disp",
                        "h5info",
                        "h5read",
                        "h5readatt",
                        "h5write",
                        "h5writeatt",
                        "hadamard",
                        "handle",
                        "hankel",
                        "hasFactoryValue",
                        "hasFrame",
                        "hasGroup",
                        "hasPersonalValue",
                        "hasSetting",
                        "hasTemporaryValue",
                        "hasdata",
                        "hasnext",
                        "hdfan",
                        "hdfdf24",
                        "hdfdfr8",
                        "hdfh",
                        "hdfhd",
                        "hdfhe",
                        "hdfhx",
                        "hdfinfo",
                        "hdfml",
                        "hdfpt",
                        "hdfread",
                        "hdfv",
                        "hdfvf",
                        "hdfvh",
                        "hdfvs",
                        "head",
                        "heatmap",
                        "height",
                        "help",
                        "helpdlg",
                        "hess",
                        "hex2dec",
                        "hex2num",
                        "hgexport",
                        "hggroup",
                        "hgtransform",
                        "hidden",
                        "highlight",
                        "hilb",
                        "histcounts",
                        "histcounts2",
                        "histogram",
                        "histogram2",
                        "hms",
                        "hold",
                        "holes",
                        "home",
                        "horzcat",
                        "hot",
                        "hour",
                        "hours",
                        "hover",
                        "hsv",
                        "hsv2rgb",
                        "hypot",
                        "i",
                        "ichol",
                        "idealfilter",
                        "idivide",
                        "ifft",
                        "ifft2",
                        "ifftn",
                        "ifftshift",
                        "ilu",
                        "im2double",
                        "im2frame",
                        "im2gray",
                        "im2java",
                        "imag",
                        "image",
                        "imageDatastore",
                        "imagesc",
                        "imapprox",
                        "imfinfo",
                        "imformats",
                        "imgCompress",
                        "import",
                        "importdata",
                        "imread",
                        "imresize",
                        "imshow",
                        "imtile",
                        "imwrite",
                        "inShape",
                        "incenter",
                        "incidence",
                        "ind2rgb",
                        "ind2sub",
                        "indegree",
                        "inedges",
                        "infoImpl",
                        "inmem",
                        "inner2outer",
                        "innerjoin",
                        "inpolygon",
                        "input",
                        "inputParser",
                        "inputdlg",
                        "inputname",
                        "insertATbl",
                        "insertAfter",
                        "insertBTbl",
                        "insertBefore",
                        "insertCol",
                        "insertImg",
                        "insertRows",
                        "int16",
                        "int2str",
                        "int32",
                        "int64",
                        "int8",
                        "integral",
                        "integral2",
                        "integral3",
                        "interp1",
                        "interp2",
                        "interp3",
                        "interpft",
                        "interpn",
                        "interpstreamspeed",
                        "intersect",
                        "intmax",
                        "intmin",
                        "inv",
                        "invhilb",
                        "ipermute",
                        "iqr",
                        "isCompressedImg",
                        "isConnected",
                        "isDiscreteStateSpecificationMutableImpl",
                        "isDone",
                        "isDoneImpl",
                        "isInactivePropertyImpl",
                        "isInputComplexityMutableImpl",
                        "isInputDataTypeMutableImpl",
                        "isInputSizeMutableImpl",
                        "isInterior",
                        "isKey",
                        "isLoaded",
                        "isLocked",
                        "isMATLABReleaseOlderThan",
                        "isPartitionable",
                        "isShuffleable",
                        "isStringScalar",
                        "isTunablePropertyDataTypeMutableImpl",
                        "isUnderlyingType",
                        "isa",
                        "isaUnderlying",
                        "isappdata",
                        "isbanded",
                        "isbetween",
                        "iscalendarduration",
                        "iscategorical",
                        "iscategory",
                        "iscell",
                        "iscellstr",
                        "ischange",
                        "ischar",
                        "iscolumn",
                        "iscom",
                        "isdag",
                        "isdatetime",
                        "isdiag",
                        "isdst",
                        "isduration",
                        "isempty",
                        "isenum",
                        "isequal",
                        "isequaln",
                        "isevent",
                        "isfield",
                        "isfile",
                        "isfinite",
                        "isfloat",
                        "isfolder",
                        "isgraphics",
                        "ishandle",
                        "ishermitian",
                        "ishold",
                        "ishole",
                        "isinf",
                        "isinteger",
                        "isinterface",
                        "isinterior",
                        "isisomorphic",
                        "isjava",
                        "iskeyword",
                        "isletter",
                        "islocalmax",
                        "islocalmin",
                        "islogical",
                        "ismac",
                        "ismatrix",
                        "ismember",
                        "ismembertol",
                        "ismethod",
                        "ismissing",
                        "ismultigraph",
                        "isnan",
                        "isnat",
                        "isnumeric",
                        "isobject",
                        "isocaps",
                        "isocolors",
                        "isomorphism",
                        "isonormals",
                        "isordinal",
                        "isosurface",
                        "isoutlier",
                        "ispc",
                        "isplaying",
                        "ispref",
                        "isprime",
                        "isprop",
                        "isprotected",
                        "isreal",
                        "isrecording",
                        "isregular",
                        "isrow",
                        "isscalar",
                        "issimplified",
                        "issorted",
                        "issortedrows",
                        "isspace",
                        "issparse",
                        "isstring",
                        "isstrprop",
                        "isstruct",
                        "isstudent",
                        "issymmetric",
                        "istable",
                        "istall",
                        "istimetable",
                        "istril",
                        "istriu",
                        "isundefined",
                        "isunix",
                        "isvalid",
                        "isvarname",
                        "isvector",
                        "isweekend",
                        "j",
                        "javaArray",
                        "javaMethod",
                        "javaMethodEDT",
                        "javaObject",
                        "javaObjectEDT",
                        "javaaddpath",
                        "javachk",
                        "javaclasspath",
                        "javarmpath",
                        "jet",
                        "join",
                        "jsondecode",
                        "jsonencode",
                        "juliandate",
                        "keyboard",
                        "keys",
                        "kron",
                        "labeledge",
                        "labelnode",
                        "lag",
                        "laplacian",
                        "lastwarn",
                        "layout",
                        "lcm",
                        "ldl",
                        "leapseconds",
                        "legend",
                        "legendre",
                        "length",
                        "letterBoundary",
                        "lettersPattern",
                        "lib.pointer",
                        "libfunctions",
                        "libfunctionsview",
                        "libisloaded",
                        "libpointer",
                        "libstruct",
                        "license",
                        "light",
                        "lightangle",
                        "lighting",
                        "lin2mu",
                        "line",
                        "lineBoundary",
                        "lines",
                        "linkaxes",
                        "linkdata",
                        "linkprop",
                        "linsolve",
                        "linspace",
                        "listModifiedFiles",
                        "listRequiredFiles",
                        "listdlg",
                        "listener",
                        "listfonts",
                        "load",
                        "loadObjectImpl",
                        "loadlibrary",
                        "loadobj",
                        "localfunctions",
                        "log",
                        "log10",
                        "log1p",
                        "log2",
                        "logical",
                        "loglog",
                        "logm",
                        "logspace",
                        "lookAheadBoundary",
                        "lookBehindBoundary",
                        "lookfor",
                        "lower",
                        "ls",
                        "lscov",
                        "lsqminnorm",
                        "lsqnonneg",
                        "lsqr",
                        "lu",
                        "magic",
                        "makehgtform",
                        "makima",
                        "mapreduce",
                        "mapreducer",
                        "maskedPattern",
                        "mat2cell",
                        "mat2str",
                        "matches",
                        "matchpairs",
                        "material",
                        "matfile",
                        "matlab.System",
                        "matlab.addons.disableAddon",
                        "matlab.addons.enableAddon",
                        "matlab.addons.install",
                        "matlab.addons.installedAddons",
                        "matlab.addons.isAddonEnabled",
                        "matlab.addons.toolbox.installToolbox",
                        "matlab.addons.toolbox.installedToolboxes",
                        "matlab.addons.toolbox.packageToolbox",
                        "matlab.addons.toolbox.toolboxVersion",
                        "matlab.addons.toolbox.uninstallToolbox",
                        "matlab.addons.uninstall",
                        "matlab.apputil.create",
                        "matlab.apputil.getInstalledAppInfo",
                        "matlab.apputil.install",
                        "matlab.apputil.package",
                        "matlab.apputil.run",
                        "matlab.apputil.uninstall",
                        "matlab.codetools.requiredFilesAndProducts",
                        "matlab.engine.FutureResult",
                        "matlab.engine.MatlabEngine",
                        "matlab.engine.connect_matlab",
                        "matlab.engine.engineName",
                        "matlab.engine.find_matlab",
                        "matlab.engine.isEngineShared",
                        "matlab.engine.shareEngine",
                        "matlab.engine.start_matlab",
                        "matlab.exception.JavaException",
                        "matlab.exception.PyException",
                        "matlab.graphics.chartcontainer.ChartContainer",
                        "matlab.graphics.chartcontainer.mixin.Colorbar",
                        "matlab.graphics.chartcontainer.mixin.Legend",
                        "matlab.io.Datastore",
                        "matlab.io.datastore.BlockedFileSet",
                        "matlab.io.datastore.DsFileReader",
                        "matlab.io.datastore.DsFileSet",
                        "matlab.io.datastore.FileSet",
                        "matlab.io.datastore.FileWritable",
                        "matlab.io.datastore.FoldersPropertyProvider",
                        "matlab.io.datastore.HadoopLocationBased",
                        "matlab.io.datastore.Partitionable",
                        "matlab.io.datastore.Shuffleable",
                        "matlab.io.hdf4.sd",
                        "matlab.io.hdfeos.gd",
                        "matlab.io.hdfeos.sw",
                        "matlab.io.saveVariablesToScript",
                        "matlab.lang.OnOffSwitchState",
                        "matlab.lang.correction.AppendArgumentsCorrection",
                        "matlab.lang.correction.ConvertToFunctionNotationCorrection",
                        "matlab.lang.correction.ReplaceIdentifierCorrection",
                        "matlab.lang.makeUniqueStrings",
                        "matlab.lang.makeValidName",
                        "matlab.mex.MexHost",
                        "matlab.mixin.Copyable",
                        "matlab.mixin.CustomDisplay",
                        "matlab.mixin.Heterogeneous",
                        "matlab.mixin.SetGet",
                        "matlab.mixin.SetGetExactNames",
                        "matlab.mixin.util.PropertyGroup",
                        "matlab.mock.AnyArguments",
                        "matlab.mock.InteractionHistory",
                        "matlab.mock.InteractionHistory.forMock",
                        "matlab.mock.MethodCallBehavior",
                        "matlab.mock.PropertyBehavior",
                        "matlab.mock.PropertyGetBehavior",
                        "matlab.mock.PropertySetBehavior",
                        "matlab.mock.TestCase",
                        "matlab.mock.actions.AssignOutputs",
                        "matlab.mock.actions.DoNothing",
                        "matlab.mock.actions.Invoke",
                        "matlab.mock.actions.ReturnStoredValue",
                        "matlab.mock.actions.StoreValue",
                        "matlab.mock.actions.ThrowException",
                        "matlab.mock.constraints.Occurred",
                        "matlab.mock.constraints.WasAccessed",
                        "matlab.mock.constraints.WasCalled",
                        "matlab.mock.constraints.WasSet",
                        "matlab.net.ArrayFormat",
                        "matlab.net.QueryParameter",
                        "matlab.net.URI",
                        "matlab.net.base64decode",
                        "matlab.net.base64encode",
                        "matlab.net.http.AuthInfo",
                        "matlab.net.http.AuthenticationScheme",
                        "matlab.net.http.Cookie",
                        "matlab.net.http.CookieInfo",
                        "matlab.net.http.Credentials",
                        "matlab.net.http.Disposition",
                        "matlab.net.http.HTTPException",
                        "matlab.net.http.HTTPOptions",
                        "matlab.net.http.HeaderField",
                        "matlab.net.http.LogRecord",
                        "matlab.net.http.MediaType",
                        "matlab.net.http.Message",
                        "matlab.net.http.MessageBody",
                        "matlab.net.http.MessageType",
                        "matlab.net.http.ProgressMonitor",
                        "matlab.net.http.ProtocolVersion",
                        "matlab.net.http.RequestLine",
                        "matlab.net.http.RequestMessage",
                        "matlab.net.http.RequestMethod",
                        "matlab.net.http.ResponseMessage",
                        "matlab.net.http.StartLine",
                        "matlab.net.http.StatusClass",
                        "matlab.net.http.StatusCode",
                        "matlab.net.http.StatusLine",
                        "matlab.net.http.field.AcceptField",
                        "matlab.net.http.field.AuthenticateField",
                        "matlab.net.http.field.AuthenticationInfoField",
                        "matlab.net.http.field.AuthorizationField",
                        "matlab.net.http.field.ContentDispositionField",
                        "matlab.net.http.field.ContentLengthField",
                        "matlab.net.http.field.ContentLocationField",
                        "matlab.net.http.field.ContentTypeField",
                        "matlab.net.http.field.CookieField",
                        "matlab.net.http.field.DateField",
                        "matlab.net.http.field.GenericField",
                        "matlab.net.http.field.GenericParameterizedField",
                        "matlab.net.http.field.HTTPDateField",
                        "matlab.net.http.field.IntegerField",
                        "matlab.net.http.field.LocationField",
                        "matlab.net.http.field.MediaRangeField",
                        "matlab.net.http.field.SetCookieField",
                        "matlab.net.http.field.URIReferenceField",
                        "matlab.net.http.io.BinaryConsumer",
                        "matlab.net.http.io.ContentConsumer",
                        "matlab.net.http.io.ContentProvider",
                        "matlab.net.http.io.FileConsumer",
                        "matlab.net.http.io.FileProvider",
                        "matlab.net.http.io.FormProvider",
                        "matlab.net.http.io.GenericConsumer",
                        "matlab.net.http.io.GenericProvider",
                        "matlab.net.http.io.ImageConsumer",
                        "matlab.net.http.io.ImageProvider",
                        "matlab.net.http.io.JSONConsumer",
                        "matlab.net.http.io.JSONProvider",
                        "matlab.net.http.io.MultipartConsumer",
                        "matlab.net.http.io.MultipartFormProvider",
                        "matlab.net.http.io.MultipartProvider",
                        "matlab.net.http.io.StringConsumer",
                        "matlab.net.http.io.StringProvider",
                        "matlab.perftest.FixedTimeExperiment",
                        "matlab.perftest.FrequentistTimeExperiment",
                        "matlab.perftest.TestCase",
                        "matlab.perftest.TimeExperiment",
                        "matlab.perftest.TimeResult",
                        "matlab.project.Project",
                        "matlab.project.convertDefinitionFiles",
                        "matlab.project.createProject",
                        "matlab.project.deleteProject",
                        "matlab.project.loadProject",
                        "matlab.project.rootProject",
                        "matlab.settings.FactoryGroup.createToolboxGroup",
                        "matlab.settings.SettingsFileUpgrader",
                        "matlab.settings.loadSettingsCompatibilityResults",
                        "matlab.settings.mustBeIntegerScalar",
                        "matlab.settings.mustBeLogicalScalar",
                        "matlab.settings.mustBeNumericScalar",
                        "matlab.settings.mustBeStringScalar",
                        "matlab.settings.reloadFactoryFile",
                        "matlab.system.mixin.FiniteSource",
                        "matlab.tall.blockMovingWindow",
                        "matlab.tall.movingWindow",
                        "matlab.tall.reduce",
                        "matlab.tall.transform",
                        "matlab.test.behavior.Missing",
                        "matlab.ui.componentcontainer.ComponentContainer",
                        "matlab.uitest.TestCase",
                        "matlab.uitest.TestCase.forInteractiveUse",
                        "matlab.uitest.unlock",
                        "matlab.unittest.Test",
                        "matlab.unittest.TestCase",
                        "matlab.unittest.TestResult",
                        "matlab.unittest.TestRunner",
                        "matlab.unittest.TestSuite",
                        "matlab.unittest.constraints.BooleanConstraint",
                        "matlab.unittest.constraints.Constraint",
                        "matlab.unittest.constraints.Tolerance",
                        "matlab.unittest.diagnostics.ConstraintDiagnostic",
                        "matlab.unittest.diagnostics.Diagnostic",
                        "matlab.unittest.fixtures.Fixture",
                        "matlab.unittest.measurement.DefaultMeasurementResult",
                        "matlab.unittest.measurement.MeasurementResult",
                        "matlab.unittest.measurement.chart.ComparisonPlot",
                        "matlab.unittest.plugins.OutputStream",
                        "matlab.unittest.plugins.Parallelizable",
                        "matlab.unittest.plugins.QualifyingPlugin",
                        "matlab.unittest.plugins.TestRunnerPlugin",
                        "matlab.wsdl.createWSDLClient",
                        "matlab.wsdl.setWSDLToolPath",
                        "matlabRelease",
                        "matlabrc",
                        "matlabroot",
                        "max",
                        "maxflow",
                        "maxk",
                        "mean",
                        "median",
                        "memmapfile",
                        "memoize",
                        "memory",
                        "mergecats",
                        "mergevars",
                        "mesh",
                        "meshc",
                        "meshgrid",
                        "meshz",
                        "meta.ArrayDimension",
                        "meta.DynamicProperty",
                        "meta.EnumeratedValue",
                        "meta.FixedDimension",
                        "meta.MetaData",
                        "meta.UnrestrictedDimension",
                        "meta.Validation",
                        "meta.abstractDetails",
                        "meta.class",
                        "meta.class.fromName",
                        "meta.event",
                        "meta.method",
                        "meta.package",
                        "meta.package.fromName",
                        "meta.package.getAllPackages",
                        "meta.property",
                        "metaclass",
                        "methods",
                        "methodsview",
                        "mex",
                        "mexext",
                        "mexhost",
                        "mfilename",
                        "mget",
                        "milliseconds",
                        "min",
                        "mink",
                        "minres",
                        "minspantree",
                        "minute",
                        "minutes",
                        "mislocked",
                        "missing",
                        "mkdir",
                        "mkpp",
                        "mldivide",
                        "mlintrpt",
                        "mlock",
                        "mmfileinfo",
                        "mod",
                        "mode",
                        "month",
                        "more",
                        "morebins",
                        "movAbsHDU",
                        "movNamHDU",
                        "movRelHDU",
                        "move",
                        "movefile",
                        "movegui",
                        "movevars",
                        "movie",
                        "movmad",
                        "movmax",
                        "movmean",
                        "movmedian",
                        "movmin",
                        "movprod",
                        "movstd",
                        "movsum",
                        "movvar",
                        "mpower",
                        "mput",
                        "mrdivide",
                        "msgbox",
                        "mtimes",
                        "mu2lin",
                        "multibandread",
                        "multibandwrite",
                        "munlock",
                        "mustBeA",
                        "mustBeFile",
                        "mustBeFinite",
                        "mustBeFloat",
                        "mustBeFolder",
                        "mustBeGreaterThan",
                        "mustBeGreaterThanOrEqual",
                        "mustBeInRange",
                        "mustBeInteger",
                        "mustBeLessThan",
                        "mustBeLessThanOrEqual",
                        "mustBeMember",
                        "mustBeNegative",
                        "mustBeNonNan",
                        "mustBeNonempty",
                        "mustBeNonmissing",
                        "mustBeNonnegative",
                        "mustBeNonpositive",
                        "mustBeNonsparse",
                        "mustBeNonzero",
                        "mustBeNonzeroLengthText",
                        "mustBeNumeric",
                        "mustBeNumericOrLogical",
                        "mustBePositive",
                        "mustBeReal",
                        "mustBeScalarOrEmpty",
                        "mustBeText",
                        "mustBeTextScalar",
                        "mustBeUnderlyingType",
                        "mustBeValidVariableName",
                        "mustBeVector",
                        "namedPattern",
                        "namedargs2cell",
                        "namelengthmax",
                        "nargin",
                        "narginchk",
                        "nargout",
                        "nargoutchk",
                        "native2unicode",
                        "nccreate",
                        "ncdisp",
                        "nchoosek",
                        "ncinfo",
                        "ncread",
                        "ncreadatt",
                        "ncwrite",
                        "ncwriteatt",
                        "ncwriteschema",
                        "ndgrid",
                        "ndims",
                        "nearest",
                        "nearestNeighbor",
                        "nearestvertex",
                        "neighbors",
                        "netcdf.abort",
                        "netcdf.close",
                        "netcdf.copyAtt",
                        "netcdf.create",
                        "netcdf.defDim",
                        "netcdf.defGrp",
                        "netcdf.defVar",
                        "netcdf.defVarChunking",
                        "netcdf.defVarDeflate",
                        "netcdf.defVarFill",
                        "netcdf.defVarFletcher32",
                        "netcdf.delAtt",
                        "netcdf.endDef",
                        "netcdf.getAtt",
                        "netcdf.getChunkCache",
                        "netcdf.getConstant",
                        "netcdf.getConstantNames",
                        "netcdf.getVar",
                        "netcdf.inq",
                        "netcdf.inqAtt",
                        "netcdf.inqAttID",
                        "netcdf.inqAttName",
                        "netcdf.inqDim",
                        "netcdf.inqDimID",
                        "netcdf.inqDimIDs",
                        "netcdf.inqFormat",
                        "netcdf.inqGrpName",
                        "netcdf.inqGrpNameFull",
                        "netcdf.inqGrpParent",
                        "netcdf.inqGrps",
                        "netcdf.inqLibVers",
                        "netcdf.inqNcid",
                        "netcdf.inqUnlimDims",
                        "netcdf.inqVar",
                        "netcdf.inqVarChunking",
                        "netcdf.inqVarDeflate",
                        "netcdf.inqVarFill",
                        "netcdf.inqVarFletcher32",
                        "netcdf.inqVarID",
                        "netcdf.inqVarIDs",
                        "netcdf.open",
                        "netcdf.putAtt",
                        "netcdf.putVar",
                        "netcdf.reDef",
                        "netcdf.renameAtt",
                        "netcdf.renameDim",
                        "netcdf.renameVar",
                        "netcdf.setChunkCache",
                        "netcdf.setDefaultFormat",
                        "netcdf.setFill",
                        "netcdf.sync",
                        "newline",
                        "newplot",
                        "nextpow2",
                        "nexttile",
                        "nnz",
                        "nonzeros",
                        "norm",
                        "normalize",
                        "normest",
                        "notify",
                        "now",
                        "nsidedpoly",
                        "nthroot",
                        "nufft",
                        "nufftn",
                        "null",
                        "num2cell",
                        "num2hex",
                        "num2ruler",
                        "num2str",
                        "numArgumentsFromSubscript",
                        "numRegions",
                        "numboundaries",
                        "numedges",
                        "numel",
                        "numnodes",
                        "numpartitions",
                        "numsides",
                        "nzmax",
                        "ode113",
                        "ode15i",
                        "ode15s",
                        "ode23",
                        "ode23s",
                        "ode23t",
                        "ode23tb",
                        "ode45",
                        "odeget",
                        "odeset",
                        "odextend",
                        "onCleanup",
                        "ones",
                        "open",
                        "openDiskFile",
                        "openFile",
                        "openProject",
                        "openfig",
                        "opengl",
                        "openvar",
                        "optimget",
                        "optimset",
                        "optionalPattern",
                        "ordeig",
                        "orderfields",
                        "ordqz",
                        "ordschur",
                        "orient",
                        "orth",
                        "outdegree",
                        "outedges",
                        "outerjoin",
                        "overlaps",
                        "overlapsrange",
                        "pack",
                        "pad",
                        "padecoef",
                        "pagectranspose",
                        "pagemtimes",
                        "pagetranspose",
                        "pan",
                        "panInteraction",
                        "parallelplot",
                        "pareto",
                        "parquetDatastore",
                        "parquetinfo",
                        "parquetread",
                        "parquetwrite",
                        "partition",
                        "parula",
                        "pascal",
                        "patch",
                        "path",
                        "pathsep",
                        "pathtool",
                        "pattern",
                        "pause",
                        "pbaspect",
                        "pcg",
                        "pchip",
                        "pcode",
                        "pcolor",
                        "pdepe",
                        "pdeval",
                        "peaks",
                        "perimeter",
                        "perl",
                        "perms",
                        "permute",
                        "pi",
                        "pie",
                        "pie3",
                        "pink",
                        "pinv",
                        "planerot",
                        "play",
                        "playblocking",
                        "plot",
                        "plot3",
                        "plotbrowser",
                        "plotedit",
                        "plotmatrix",
                        "plottools",
                        "plus",
                        "pointLocation",
                        "pol2cart",
                        "polaraxes",
                        "polarbubblechart",
                        "polarhistogram",
                        "polarplot",
                        "polarscatter",
                        "poly",
                        "polyarea",
                        "polybuffer",
                        "polyder",
                        "polyeig",
                        "polyfit",
                        "polyint",
                        "polyshape",
                        "polyval",
                        "polyvalm",
                        "posixtime",
                        "possessivePattern",
                        "pow2",
                        "ppval",
                        "predecessors",
                        "prefdir",
                        "preferences",
                        "press",
                        "preview",
                        "primes",
                        "print",
                        "printdlg",
                        "printopt",
                        "printpreview",
                        "prism",
                        "processInputSpecificationChangeImpl",
                        "processTunedPropertiesImpl",
                        "prod",
                        "profile",
                        "propedit",
                        "properties",
                        "propertyeditor",
                        "psi",
                        "publish",
                        "pwd",
                        "pyargs",
                        "pyenv",
                        "qmr",
                        "qr",
                        "qrdelete",
                        "qrinsert",
                        "qrupdate",
                        "quad2d",
                        "quadgk",
                        "quarter",
                        "questdlg",
                        "quit",
                        "quiver",
                        "quiver3",
                        "qz",
                        "rad2deg",
                        "rand",
                        "randi",
                        "randn",
                        "randperm",
                        "rank",
                        "rat",
                        "rats",
                        "rbbox",
                        "rcond",
                        "read",
                        "readATblHdr",
                        "readBTblHdr",
                        "readCard",
                        "readCol",
                        "readFrame",
                        "readImg",
                        "readKey",
                        "readKeyCmplx",
                        "readKeyDbl",
                        "readKeyLongLong",
                        "readKeyLongStr",
                        "readKeyUnit",
                        "readRecord",
                        "readall",
                        "readcell",
                        "readline",
                        "readlines",
                        "readmatrix",
                        "readstruct",
                        "readtable",
                        "readtimetable",
                        "readvars",
                        "real",
                        "reallog",
                        "realmax",
                        "realmin",
                        "realpow",
                        "realsqrt",
                        "record",
                        "recordblocking",
                        "rectangle",
                        "rectint",
                        "recycle",
                        "reducepatch",
                        "reducevolume",
                        "refresh",
                        "refreshSourceControl",
                        "refreshdata",
                        "regexp",
                        "regexpPattern",
                        "regexpi",
                        "regexprep",
                        "regexptranslate",
                        "regionZoomInteraction",
                        "regions",
                        "registerevent",
                        "regmatlabserver",
                        "rehash",
                        "relationaloperators",
                        "release",
                        "releaseImpl",
                        "reload",
                        "rem",
                        "remove",
                        "removeCategory",
                        "removeFile",
                        "removeGroup",
                        "removeLabel",
                        "removePath",
                        "removeReference",
                        "removeSetting",
                        "removeShortcut",
                        "removeShutdownFile",
                        "removeStartupFile",
                        "removeStyle",
                        "removeToolbarExplorationButtons",
                        "removecats",
                        "removets",
                        "removevars",
                        "rename",
                        "renamecats",
                        "renamevars",
                        "rendererinfo",
                        "reordercats",
                        "reordernodes",
                        "repelem",
                        "replace",
                        "replaceBetween",
                        "repmat",
                        "resample",
                        "rescale",
                        "reset",
                        "resetImpl",
                        "reshape",
                        "residue",
                        "restoredefaultpath",
                        "resume",
                        "rethrow",
                        "retime",
                        "reverse",
                        "rgb2gray",
                        "rgb2hsv",
                        "rgb2ind",
                        "rgbplot",
                        "ribbon",
                        "rlim",
                        "rmappdata",
                        "rmboundary",
                        "rmdir",
                        "rmedge",
                        "rmfield",
                        "rmholes",
                        "rmmissing",
                        "rmnode",
                        "rmoutliers",
                        "rmpath",
                        "rmpref",
                        "rmprop",
                        "rmslivers",
                        "rng",
                        "roots",
                        "rosser",
                        "rot90",
                        "rotate",
                        "rotate3d",
                        "rotateInteraction",
                        "round",
                        "rowfun",
                        "rows2vars",
                        "rref",
                        "rsf2csf",
                        "rtickangle",
                        "rtickformat",
                        "rticklabels",
                        "rticks",
                        "ruler2num",
                        "rulerPanInteraction",
                        "run",
                        "runChecks",
                        "runperf",
                        "runtests",
                        "save",
                        "saveObjectImpl",
                        "saveas",
                        "savefig",
                        "saveobj",
                        "savepath",
                        "scale",
                        "scatter",
                        "scatter3",
                        "scatteredInterpolant",
                        "scatterhistogram",
                        "schur",
                        "scroll",
                        "sec",
                        "secd",
                        "sech",
                        "second",
                        "seconds",
                        "semilogx",
                        "semilogy",
                        "sendmail",
                        "serialport",
                        "serialportlist",
                        "set",
                        "setBscale",
                        "setCompressionType",
                        "setDTR",
                        "setHCompScale",
                        "setHCompSmooth",
                        "setProperties",
                        "setRTS",
                        "setTileDim",
                        "setTscale",
                        "setabstime",
                        "setappdata",
                        "setcats",
                        "setdiff",
                        "setenv",
                        "setfield",
                        "setinterpmethod",
                        "setpixelposition",
                        "setpref",
                        "settimeseriesnames",
                        "settings",
                        "setuniformtime",
                        "setup",
                        "setupImpl",
                        "setvaropts",
                        "setvartype",
                        "setxor",
                        "sgtitle",
                        "shading",
                        "sheetnames",
                        "shg",
                        "shiftdim",
                        "shortestpath",
                        "shortestpathtree",
                        "showplottool",
                        "shrinkfaces",
                        "shuffle",
                        "sign",
                        "simplify",
                        "sin",
                        "sind",
                        "single",
                        "sinh",
                        "sinpi",
                        "size",
                        "slice",
                        "smooth3",
                        "smoothdata",
                        "snapnow",
                        "sort",
                        "sortboundaries",
                        "sortregions",
                        "sortrows",
                        "sortx",
                        "sorty",
                        "sound",
                        "soundsc",
                        "spalloc",
                        "sparse",
                        "spaugment",
                        "spconvert",
                        "spdiags",
                        "specular",
                        "speye",
                        "spfun",
                        "sph2cart",
                        "sphere",
                        "spinmap",
                        "spline",
                        "split",
                        "splitapply",
                        "splitlines",
                        "splitvars",
                        "spones",
                        "spparms",
                        "sprand",
                        "sprandn",
                        "sprandsym",
                        "sprank",
                        "spreadsheetDatastore",
                        "spreadsheetImportOptions",
                        "spring",
                        "sprintf",
                        "spy",
                        "sqrt",
                        "sqrtm",
                        "squeeze",
                        "ss2tf",
                        "sscanf",
                        "stack",
                        "stackedplot",
                        "stairs",
                        "standardizeMissing",
                        "start",
                        "startat",
                        "startsWith",
                        "startup",
                        "std",
                        "stem",
                        "stem3",
                        "step",
                        "stepImpl",
                        "stlread",
                        "stlwrite",
                        "stop",
                        "str2double",
                        "str2func",
                        "str2num",
                        "strcat",
                        "strcmp",
                        "strcmpi",
                        "stream2",
                        "stream3",
                        "streamline",
                        "streamparticles",
                        "streamribbon",
                        "streamslice",
                        "streamtube",
                        "strfind",
                        "string",
                        "strings",
                        "strip",
                        "strjoin",
                        "strjust",
                        "strlength",
                        "strncmp",
                        "strncmpi",
                        "strrep",
                        "strsplit",
                        "strtok",
                        "strtrim",
                        "struct",
                        "struct2cell",
                        "struct2table",
                        "structfun",
                        "sub2ind",
                        "subgraph",
                        "subplot",
                        "subsasgn",
                        "subscribe",
                        "subsindex",
                        "subspace",
                        "subsref",
                        "substruct",
                        "subtitle",
                        "subtract",
                        "subvolume",
                        "successors",
                        "sum",
                        "summary",
                        "summer",
                        "superclasses",
                        "surf",
                        "surf2patch",
                        "surface",
                        "surfaceArea",
                        "surfc",
                        "surfl",
                        "surfnorm",
                        "svd",
                        "svds",
                        "svdsketch",
                        "swapbytes",
                        "swarmchart",
                        "swarmchart3",
                        "sylvester",
                        "symamd",
                        "symbfact",
                        "symmlq",
                        "symrcm",
                        "synchronize",
                        "sysobjupdate",
                        "system",
                        "table",
                        "table2array",
                        "table2cell",
                        "table2struct",
                        "table2timetable",
                        "tabularTextDatastore",
                        "tail",
                        "tall",
                        "tallrng",
                        "tan",
                        "tand",
                        "tanh",
                        "tar",
                        "tcpclient",
                        "tempdir",
                        "tempname",
                        "testsuite",
                        "tetramesh",
                        "texlabel",
                        "text",
                        "textBoundary",
                        "textscan",
                        "textwrap",
                        "tfqmr",
                        "thetalim",
                        "thetatickformat",
                        "thetaticklabels",
                        "thetaticks",
                        "thingSpeakRead",
                        "thingSpeakWrite",
                        "throw",
                        "throwAsCaller",
                        "tic",
                        "tiledlayout",
                        "time",
                        "timeit",
                        "timeofday",
                        "timer",
                        "timerange",
                        "timerfind",
                        "timerfindall",
                        "timeseries",
                        "timetable",
                        "timetable2table",
                        "timezones",
                        "title",
                        "toc",
                        "todatenum",
                        "toeplitz",
                        "toolboxdir",
                        "topkrows",
                        "toposort",
                        "trace",
                        "transclosure",
                        "transform",
                        "translate",
                        "transpose",
                        "transreduction",
                        "trapz",
                        "treelayout",
                        "treeplot",
                        "triangulation",
                        "tril",
                        "trimesh",
                        "triplot",
                        "trisurf",
                        "triu",
                        "true",
                        "tscollection",
                        "tsdata.event",
                        "tsearchn",
                        "turbo",
                        "turningdist",
                        "type",
                        "typecast",
                        "tzoffset",
                        "uialert",
                        "uiaxes",
                        "uibutton",
                        "uibuttongroup",
                        "uicheckbox",
                        "uiconfirm",
                        "uicontextmenu",
                        "uicontrol",
                        "uidatepicker",
                        "uidropdown",
                        "uieditfield",
                        "uifigure",
                        "uigauge",
                        "uigetdir",
                        "uigetfile",
                        "uigetpref",
                        "uigridlayout",
                        "uihtml",
                        "uiimage",
                        "uiknob",
                        "uilabel",
                        "uilamp",
                        "uilistbox",
                        "uimenu",
                        "uint16",
                        "uint32",
                        "uint64",
                        "uint8",
                        "uiopen",
                        "uipanel",
                        "uiprogressdlg",
                        "uipushtool",
                        "uiputfile",
                        "uiradiobutton",
                        "uiresume",
                        "uisave",
                        "uisetcolor",
                        "uisetfont",
                        "uisetpref",
                        "uislider",
                        "uispinner",
                        "uistack",
                        "uistyle",
                        "uiswitch",
                        "uitab",
                        "uitabgroup",
                        "uitable",
                        "uitextarea",
                        "uitogglebutton",
                        "uitoggletool",
                        "uitoolbar",
                        "uitree",
                        "uitreenode",
                        "uiwait",
                        "uminus",
                        "underlyingType",
                        "underlyingValue",
                        "unicode2native",
                        "union",
                        "unique",
                        "uniquetol",
                        "unix",
                        "unloadlibrary",
                        "unmesh",
                        "unmkpp",
                        "unregisterallevents",
                        "unregisterevent",
                        "unstack",
                        "unsubscribe",
                        "untar",
                        "unwrap",
                        "unzip",
                        "update",
                        "updateDependencies",
                        "uplus",
                        "upper",
                        "usejava",
                        "userpath",
                        "validateFunctionSignaturesJSON",
                        "validateInputsImpl",
                        "validatePropertiesImpl",
                        "validateattributes",
                        "validatecolor",
                        "validatestring",
                        "values",
                        "vander",
                        "var",
                        "varargin",
                        "varargout",
                        "varfun",
                        "vartype",
                        "vecnorm",
                        "ver",
                        "verLessThan",
                        "version",
                        "vertcat",
                        "vertexAttachments",
                        "vertexNormal",
                        "view",
                        "viewmtx",
                        "visdiff",
                        "volume",
                        "volumebounds",
                        "voronoi",
                        "voronoiDiagram",
                        "voronoin",
                        "wait",
                        "waitbar",
                        "waitfor",
                        "waitforbuttonpress",
                        "warndlg",
                        "warning",
                        "waterfall",
                        "web",
                        "weboptions",
                        "webread",
                        "websave",
                        "webwrite",
                        "week",
                        "weekday",
                        "what",
                        "which",
                        "whitespaceBoundary",
                        "whitespacePattern",
                        "who",
                        "whos",
                        "width",
                        "wildcardPattern",
                        "wilkinson",
                        "winopen",
                        "winqueryreg",
                        "winter",
                        "withinrange",
                        "withtol",
                        "wordcloud",
                        "write",
                        "writeChecksum",
                        "writeCol",
                        "writeComment",
                        "writeDate",
                        "writeHistory",
                        "writeImg",
                        "writeKey",
                        "writeKeyUnit",
                        "writeVideo",
                        "writeall",
                        "writecell",
                        "writeline",
                        "writematrix",
                        "writestruct",
                        "writetable",
                        "writetimetable",
                        "xcorr",
                        "xcov",
                        "xlabel",
                        "xlim",
                        "xline",
                        "xmlread",
                        "xmlwrite",
                        "xor",
                        "xslt",
                        "xtickangle",
                        "xtickformat",
                        "xticklabels",
                        "xticks",
                        "year",
                        "years",
                        "ylabel",
                        "ylim",
                        "yline",
                        "ymd",
                        "ytickangle",
                        "ytickformat",
                        "yticklabels",
                        "yticks",
                        "yyaxis",
                        "yyyymmdd",
                        "zeros",
                        "zip",
                        "zlabel",
                        "zlim",
                        "zoom",
                        "zoomInteraction",
                        "ztickangle",
                        "ztickformat",
                        "zticklabels",
                        "zticks",
                    ],
                    prefix=r"(?<!\.)(",  # Exclude field names
                    suffix=r")\b"
                ),
                Name.Builtin
            ),

            # line continuation with following comment:
            (r'(\.\.\.)(.*)$', bygroups(Keyword, Comment)),

            # command form:
            # "How MATLAB Recognizes Command Syntax" specifies that an operator
            # is recognized if it is either surrounded by spaces or by no
            # spaces on both sides (this allows distinguishing `cd ./foo` from
            # `cd ./ foo`.).  Here, the regex checks that the first word in the
            # line is not followed by <spaces> and then
            # (equal | open-parenthesis | <operator><space> | <space>).
            (rf'(?:^|(?<=;))(\s*)(\w+)(\s+)(?!=|\(|{_operators}\s|\s)',
             bygroups(Whitespace, Name, Whitespace), 'commandargs'),

            include('expressions')
        ],
        'blockcomment': [
            (r'^\s*%\}', Comment.Multiline, '#pop'),
            (r'^.*\n', Comment.Multiline),
            (r'.', Comment.Multiline),
        ],
        'deffunc': [
            (r'(\s*)(?:(\S+)(\s*)(=)(\s*))?(.+)(\()(.*)(\))(\s*)',
             bygroups(Whitespace, Text, Whitespace, Punctuation,
                      Whitespace, Name.Function, Punctuation, Text,
                      Punctuation, Whitespace), '#pop'),
            # function with no args
            (r'(\s*)([a-zA-Z_]\w*)',
             bygroups(Whitespace, Name.Function), '#pop'),
        ],
        'propattrs': [
            (r'(\w+)(\s*)(=)(\s*)(\d+)',
             bygroups(Name.Builtin, Whitespace, Punctuation, Whitespace,
                      Number)),
            (r'(\w+)(\s*)(=)(\s*)([a-zA-Z]\w*)',
             bygroups(Name.Builtin, Whitespace, Punctuation, Whitespace,
                      Keyword)),
            (r',', Punctuation),
            (r'\)', Punctuation, '#pop'),
            (r'\s+', Whitespace),
            (r'.', Text),
        ],
        'defprops': [
            (r'%\{\s*\n', Comment.Multiline, 'blockcomment'),
            (r'%.*$', Comment),
            (r'(?<!\.)end\b', Keyword, '#pop'),
            include('expressions'),
        ],
        'string': [
            (r"[^']*'", String, '#pop'),
        ],
        'commandargs': [
            # If an equal sign or other operator is encountered, this
            # isn't a command. It might be a variable assignment or
            # comparison operation with multiple spaces before the
            # equal sign or operator
            (r"=", Punctuation, '#pop'),
            (_operators, Operator, '#pop'),
            (r"[ \t]+", Whitespace),
            ("'[^']*'", String),
            (r"[^';\s]+", String),
            (";", Punctuation, '#pop'),
            default('#pop'),
        ]
    }

    def analyse_text(text):
        # function declaration.
        first_non_comment = next((line for line in text.splitlines()
                                  if not re.match(r'^\s*%', text)), '').strip()
        if (first_non_comment.startswith('function')
                and '{' not in first_non_comment):
            return 1.
        # comment
        elif re.search(r'^\s*%', text, re.M):
            return 0.2
        # system cmd
        elif re.search(r'^!\w+', text, re.M):
            return 0.2


line_re  = re.compile('.*?\n')


class MatlabSessionLexer(Lexer):
    """
    For Matlab sessions.  Modeled after PythonConsoleLexer.
    Contributed by Ken Schutte <<EMAIL>>.
    """
    name = 'Matlab session'
    aliases = ['matlabsession']
    url = 'https://www.mathworks.com/products/matlab.html'
    version_added = '0.10'
    _example = "matlabsession/matlabsession_sample.txt"

    def get_tokens_unprocessed(self, text):
        mlexer = MatlabLexer(**self.options)

        curcode = ''
        insertions = []
        continuation = False

        for match in line_re.finditer(text):
            line = match.group()

            if line.startswith('>> '):
                insertions.append((len(curcode),
                                   [(0, Generic.Prompt, line[:3])]))
                curcode += line[3:]

            elif line.startswith('>>'):
                insertions.append((len(curcode),
                                   [(0, Generic.Prompt, line[:2])]))
                curcode += line[2:]

            elif line.startswith('???'):

                idx = len(curcode)

                # without is showing error on same line as before...?
                # line = "\n" + line
                token = (0, Generic.Traceback, line)
                insertions.append((idx, [token]))
            elif continuation and insertions:
                # line_start is the length of the most recent prompt symbol
                line_start = len(insertions[-1][-1][-1])
                # Set leading spaces with the length of the prompt to be a generic prompt
                # This keeps code aligned when prompts are removed, say with some Javascript
                if line.startswith(' '*line_start):
                    insertions.append(
                        (len(curcode), [(0, Generic.Prompt, line[:line_start])]))
                    curcode += line[line_start:]
                else:
                    curcode += line
            else:
                if curcode:
                    yield from do_insertions(
                        insertions, mlexer.get_tokens_unprocessed(curcode))
                    curcode = ''
                    insertions = []

                yield match.start(), Generic.Output, line

            # Does not allow continuation if a comment is included after the ellipses.
            # Continues any line that ends with ..., even comments (lines that start with %)
            if line.strip().endswith('...'):
                continuation = True
            else:
                continuation = False

        if curcode:  # or item:
            yield from do_insertions(
                insertions, mlexer.get_tokens_unprocessed(curcode))


class OctaveLexer(RegexLexer):
    """
    For GNU Octave source code.
    """
    name = 'Octave'
    url = 'https://www.gnu.org/software/octave/index'
    aliases = ['octave']
    filenames = ['*.m']
    mimetypes = ['text/octave']
    version_added = '1.5'

    # These lists are generated automatically.
    # Run the following in bash shell:
    #
    # First dump all of the Octave manual into a plain text file:
    #
    #   $ info octave --subnodes -o octave-manual
    #
    # Now grep through it:

    # for i in \
    #     "Built-in Function" "Command" "Function File" \
    #     "Loadable Function" "Mapping Function";
    # do
    #     perl -e '@name = qw('"$i"');
    #              print lc($name[0]),"_kw = [\n"';
    #
    #     perl -n -e 'print "\"$1\",\n" if /-- '"$i"': .* (\w*) \(/;' \
    #         octave-manual | sort | uniq ;
    #     echo "]" ;
    #     echo;
    # done

    # taken from Octave Mercurial changeset 8cc154f45e37 (30-jan-2011)

    builtin_kw = (
        "addlistener", "addpath", "addproperty", "all",
        "and", "any", "argnames", "argv", "assignin",
        "atexit", "autoload",
        "available_graphics_toolkits", "beep_on_error",
        "bitand", "bitmax", "bitor", "bitshift", "bitxor",
        "cat", "cell", "cellstr", "char", "class", "clc",
        "columns", "command_line_path",
        "completion_append_char", "completion_matches",
        "complex", "confirm_recursive_rmdir", "cputime",
        "crash_dumps_octave_core", "ctranspose", "cumprod",
        "cumsum", "debug_on_error", "debug_on_interrupt",
        "debug_on_warning", "default_save_options",
        "dellistener", "diag", "diff", "disp",
        "doc_cache_file", "do_string_escapes", "double",
        "drawnow", "e", "echo_executing_commands", "eps",
        "eq", "errno", "errno_list", "error", "eval",
        "evalin", "exec", "exist", "exit", "eye", "false",
        "fclear", "fclose", "fcntl", "fdisp", "feof",
        "ferror", "feval", "fflush", "fgetl", "fgets",
        "fieldnames", "file_in_loadpath", "file_in_path",
        "filemarker", "filesep", "find_dir_in_path",
        "fixed_point_format", "fnmatch", "fopen", "fork",
        "formula", "fprintf", "fputs", "fread", "freport",
        "frewind", "fscanf", "fseek", "fskipl", "ftell",
        "functions", "fwrite", "ge", "genpath", "get",
        "getegid", "getenv", "geteuid", "getgid",
        "getpgrp", "getpid", "getppid", "getuid", "glob",
        "gt", "gui_mode", "history_control",
        "history_file", "history_size",
        "history_timestamp_format_string", "home",
        "horzcat", "hypot", "ifelse",
        "ignore_function_time_stamp", "inferiorto",
        "info_file", "info_program", "inline", "input",
        "intmax", "intmin", "ipermute",
        "is_absolute_filename", "isargout", "isbool",
        "iscell", "iscellstr", "ischar", "iscomplex",
        "isempty", "isfield", "isfloat", "isglobal",
        "ishandle", "isieee", "isindex", "isinteger",
        "islogical", "ismatrix", "ismethod", "isnull",
        "isnumeric", "isobject", "isreal",
        "is_rooted_relative_filename", "issorted",
        "isstruct", "isvarname", "kbhit", "keyboard",
        "kill", "lasterr", "lasterror", "lastwarn",
        "ldivide", "le", "length", "link", "linspace",
        "logical", "lstat", "lt", "make_absolute_filename",
        "makeinfo_program", "max_recursion_depth", "merge",
        "methods", "mfilename", "minus", "mislocked",
        "mkdir", "mkfifo", "mkstemp", "mldivide", "mlock",
        "mouse_wheel_zoom", "mpower", "mrdivide", "mtimes",
        "munlock", "nargin", "nargout",
        "native_float_format", "ndims", "ne", "nfields",
        "nnz", "norm", "not", "numel", "nzmax",
        "octave_config_info", "octave_core_file_limit",
        "octave_core_file_name",
        "octave_core_file_options", "ones", "or",
        "output_max_field_width", "output_precision",
        "page_output_immediately", "page_screen_output",
        "path", "pathsep", "pause", "pclose", "permute",
        "pi", "pipe", "plus", "popen", "power",
        "print_empty_dimensions", "printf",
        "print_struct_array_contents", "prod",
        "program_invocation_name", "program_name",
        "putenv", "puts", "pwd", "quit", "rats", "rdivide",
        "readdir", "readlink", "read_readline_init_file",
        "realmax", "realmin", "rehash", "rename",
        "repelems", "re_read_readline_init_file", "reset",
        "reshape", "resize", "restoredefaultpath",
        "rethrow", "rmdir", "rmfield", "rmpath", "rows",
        "save_header_format_string", "save_precision",
        "saving_history", "scanf", "set", "setenv",
        "shell_cmd", "sighup_dumps_octave_core",
        "sigterm_dumps_octave_core", "silent_functions",
        "single", "size", "size_equal", "sizemax",
        "sizeof", "sleep", "source", "sparse_auto_mutate",
        "split_long_rows", "sprintf", "squeeze", "sscanf",
        "stat", "stderr", "stdin", "stdout", "strcmp",
        "strcmpi", "string_fill_char", "strncmp",
        "strncmpi", "struct", "struct_levels_to_print",
        "strvcat", "subsasgn", "subsref", "sum", "sumsq",
        "superiorto", "suppress_verbose_help_message",
        "symlink", "system", "tic", "tilde_expand",
        "times", "tmpfile", "tmpnam", "toc", "toupper",
        "transpose", "true", "typeinfo", "umask", "uminus",
        "uname", "undo_string_escapes", "unlink", "uplus",
        "upper", "usage", "usleep", "vec", "vectorize",
        "vertcat", "waitpid", "warning", "warranty",
        "whos_line_format", "yes_or_no", "zeros",
        "inf", "Inf", "nan", "NaN")

    command_kw = ("close", "load", "who", "whos")

    function_kw = (
        "accumarray", "accumdim", "acosd", "acotd",
        "acscd", "addtodate", "allchild", "ancestor",
        "anova", "arch_fit", "arch_rnd", "arch_test",
        "area", "arma_rnd", "arrayfun", "ascii", "asctime",
        "asecd", "asind", "assert", "atand",
        "autoreg_matrix", "autumn", "axes", "axis", "bar",
        "barh", "bartlett", "bartlett_test", "beep",
        "betacdf", "betainv", "betapdf", "betarnd",
        "bicgstab", "bicubic", "binary", "binocdf",
        "binoinv", "binopdf", "binornd", "bitcmp",
        "bitget", "bitset", "blackman", "blanks",
        "blkdiag", "bone", "box", "brighten", "calendar",
        "cast", "cauchy_cdf", "cauchy_inv", "cauchy_pdf",
        "cauchy_rnd", "caxis", "celldisp", "center", "cgs",
        "chisquare_test_homogeneity",
        "chisquare_test_independence", "circshift", "cla",
        "clabel", "clf", "clock", "cloglog", "closereq",
        "colon", "colorbar", "colormap", "colperm",
        "comet", "common_size", "commutation_matrix",
        "compan", "compare_versions", "compass",
        "computer", "cond", "condest", "contour",
        "contourc", "contourf", "contrast", "conv",
        "convhull", "cool", "copper", "copyfile", "cor",
        "corrcoef", "cor_test", "cosd", "cotd", "cov",
        "cplxpair", "cross", "cscd", "cstrcat", "csvread",
        "csvwrite", "ctime", "cumtrapz", "curl", "cut",
        "cylinder", "date", "datenum", "datestr",
        "datetick", "datevec", "dblquad", "deal",
        "deblank", "deconv", "delaunay", "delaunayn",
        "delete", "demo", "detrend", "diffpara", "diffuse",
        "dir", "discrete_cdf", "discrete_inv",
        "discrete_pdf", "discrete_rnd", "display",
        "divergence", "dlmwrite", "dos", "dsearch",
        "dsearchn", "duplication_matrix", "durbinlevinson",
        "ellipsoid", "empirical_cdf", "empirical_inv",
        "empirical_pdf", "empirical_rnd", "eomday",
        "errorbar", "etime", "etreeplot", "example",
        "expcdf", "expinv", "expm", "exppdf", "exprnd",
        "ezcontour", "ezcontourf", "ezmesh", "ezmeshc",
        "ezplot", "ezpolar", "ezsurf", "ezsurfc", "factor",
        "factorial", "fail", "fcdf", "feather", "fftconv",
        "fftfilt", "fftshift", "figure", "fileattrib",
        "fileparts", "fill", "findall", "findobj",
        "findstr", "finv", "flag", "flipdim", "fliplr",
        "flipud", "fpdf", "fplot", "fractdiff", "freqz",
        "freqz_plot", "frnd", "fsolve",
        "f_test_regression", "ftp", "fullfile", "fzero",
        "gamcdf", "gaminv", "gampdf", "gamrnd", "gca",
        "gcbf", "gcbo", "gcf", "genvarname", "geocdf",
        "geoinv", "geopdf", "geornd", "getfield", "ginput",
        "glpk", "gls", "gplot", "gradient",
        "graphics_toolkit", "gray", "grid", "griddata",
        "griddatan", "gtext", "gunzip", "gzip", "hadamard",
        "hamming", "hankel", "hanning", "hggroup",
        "hidden", "hilb", "hist", "histc", "hold", "hot",
        "hotelling_test", "housh", "hsv", "hurst",
        "hygecdf", "hygeinv", "hygepdf", "hygernd",
        "idivide", "ifftshift", "image", "imagesc",
        "imfinfo", "imread", "imshow", "imwrite", "index",
        "info", "inpolygon", "inputname", "interpft",
        "interpn", "intersect", "invhilb", "iqr", "isa",
        "isdefinite", "isdir", "is_duplicate_entry",
        "isequal", "isequalwithequalnans", "isfigure",
        "ishermitian", "ishghandle", "is_leap_year",
        "isletter", "ismac", "ismember", "ispc", "isprime",
        "isprop", "isscalar", "issquare", "isstrprop",
        "issymmetric", "isunix", "is_valid_file_id",
        "isvector", "jet", "kendall",
        "kolmogorov_smirnov_cdf",
        "kolmogorov_smirnov_test", "kruskal_wallis_test",
        "krylov", "kurtosis", "laplace_cdf", "laplace_inv",
        "laplace_pdf", "laplace_rnd", "legend", "legendre",
        "license", "line", "linkprop", "list_primes",
        "loadaudio", "loadobj", "logistic_cdf",
        "logistic_inv", "logistic_pdf", "logistic_rnd",
        "logit", "loglog", "loglogerr", "logm", "logncdf",
        "logninv", "lognpdf", "lognrnd", "logspace",
        "lookfor", "ls_command", "lsqnonneg", "magic",
        "mahalanobis", "manova", "matlabroot",
        "mcnemar_test", "mean", "meansq", "median", "menu",
        "mesh", "meshc", "meshgrid", "meshz", "mexext",
        "mget", "mkpp", "mode", "moment", "movefile",
        "mpoles", "mput", "namelengthmax", "nargchk",
        "nargoutchk", "nbincdf", "nbininv", "nbinpdf",
        "nbinrnd", "nchoosek", "ndgrid", "newplot", "news",
        "nonzeros", "normcdf", "normest", "norminv",
        "normpdf", "normrnd", "now", "nthroot", "null",
        "ocean", "ols", "onenormest", "optimget",
        "optimset", "orderfields", "orient", "orth",
        "pack", "pareto", "parseparams", "pascal", "patch",
        "pathdef", "pcg", "pchip", "pcolor", "pcr",
        "peaks", "periodogram", "perl", "perms", "pie",
        "pink", "planerot", "playaudio", "plot",
        "plotmatrix", "plotyy", "poisscdf", "poissinv",
        "poisspdf", "poissrnd", "polar", "poly",
        "polyaffine", "polyarea", "polyderiv", "polyfit",
        "polygcd", "polyint", "polyout", "polyreduce",
        "polyval", "polyvalm", "postpad", "powerset",
        "ppder", "ppint", "ppjumps", "ppplot", "ppval",
        "pqpnonneg", "prepad", "primes", "print",
        "print_usage", "prism", "probit", "qp", "qqplot",
        "quadcc", "quadgk", "quadl", "quadv", "quiver",
        "qzhess", "rainbow", "randi", "range", "rank",
        "ranks", "rat", "reallog", "realpow", "realsqrt",
        "record", "rectangle_lw", "rectangle_sw",
        "rectint", "refresh", "refreshdata",
        "regexptranslate", "repmat", "residue", "ribbon",
        "rindex", "roots", "rose", "rosser", "rotdim",
        "rref", "run", "run_count", "rundemos", "run_test",
        "runtests", "saveas", "saveaudio", "saveobj",
        "savepath", "scatter", "secd", "semilogx",
        "semilogxerr", "semilogy", "semilogyerr",
        "setaudio", "setdiff", "setfield", "setxor",
        "shading", "shift", "shiftdim", "sign_test",
        "sinc", "sind", "sinetone", "sinewave", "skewness",
        "slice", "sombrero", "sortrows", "spaugment",
        "spconvert", "spdiags", "spearman", "spectral_adf",
        "spectral_xdf", "specular", "speed", "spencer",
        "speye", "spfun", "sphere", "spinmap", "spline",
        "spones", "sprand", "sprandn", "sprandsym",
        "spring", "spstats", "spy", "sqp", "stairs",
        "statistics", "std", "stdnormal_cdf",
        "stdnormal_inv", "stdnormal_pdf", "stdnormal_rnd",
        "stem", "stft", "strcat", "strchr", "strjust",
        "strmatch", "strread", "strsplit", "strtok",
        "strtrim", "strtrunc", "structfun", "studentize",
        "subplot", "subsindex", "subspace", "substr",
        "substruct", "summer", "surf", "surface", "surfc",
        "surfl", "surfnorm", "svds", "swapbytes",
        "sylvester_matrix", "symvar", "synthesis", "table",
        "tand", "tar", "tcdf", "tempdir", "tempname",
        "test", "text", "textread", "textscan", "tinv",
        "title", "toeplitz", "tpdf", "trace", "trapz",
        "treelayout", "treeplot", "triangle_lw",
        "triangle_sw", "tril", "trimesh", "triplequad",
        "triplot", "trisurf", "triu", "trnd", "tsearchn",
        "t_test", "t_test_regression", "type", "unidcdf",
        "unidinv", "unidpdf", "unidrnd", "unifcdf",
        "unifinv", "unifpdf", "unifrnd", "union", "unique",
        "unix", "unmkpp", "unpack", "untabify", "untar",
        "unwrap", "unzip", "u_test", "validatestring",
        "vander", "var", "var_test", "vech", "ver",
        "version", "view", "voronoi", "voronoin",
        "waitforbuttonpress", "wavread", "wavwrite",
        "wblcdf", "wblinv", "wblpdf", "wblrnd", "weekday",
        "welch_test", "what", "white", "whitebg",
        "wienrnd", "wilcoxon_test", "wilkinson", "winter",
        "xlabel", "xlim", "ylabel", "yulewalker", "zip",
        "zlabel", "z_test")

    loadable_kw = (
        "airy", "amd", "balance", "besselh", "besseli",
        "besselj", "besselk", "bessely", "bitpack",
        "bsxfun", "builtin", "ccolamd", "cellfun",
        "cellslices", "chol", "choldelete", "cholinsert",
        "cholinv", "cholshift", "cholupdate", "colamd",
        "colloc", "convhulln", "convn", "csymamd",
        "cummax", "cummin", "daspk", "daspk_options",
        "dasrt", "dasrt_options", "dassl", "dassl_options",
        "dbclear", "dbdown", "dbstack", "dbstatus",
        "dbstop", "dbtype", "dbup", "dbwhere", "det",
        "dlmread", "dmperm", "dot", "eig", "eigs",
        "endgrent", "endpwent", "etree", "fft", "fftn",
        "fftw", "filter", "find", "full", "gcd",
        "getgrent", "getgrgid", "getgrnam", "getpwent",
        "getpwnam", "getpwuid", "getrusage", "givens",
        "gmtime", "gnuplot_binary", "hess", "ifft",
        "ifftn", "inv", "isdebugmode", "issparse", "kron",
        "localtime", "lookup", "lsode", "lsode_options",
        "lu", "luinc", "luupdate", "matrix_type", "max",
        "min", "mktime", "pinv", "qr", "qrdelete",
        "qrinsert", "qrshift", "qrupdate", "quad",
        "quad_options", "qz", "rand", "rande", "randg",
        "randn", "randp", "randperm", "rcond", "regexp",
        "regexpi", "regexprep", "schur", "setgrent",
        "setpwent", "sort", "spalloc", "sparse", "spparms",
        "sprank", "sqrtm", "strfind", "strftime",
        "strptime", "strrep", "svd", "svd_driver", "syl",
        "symamd", "symbfact", "symrcm", "time", "tsearch",
        "typecast", "urlread", "urlwrite")

    mapping_kw = (
        "abs", "acos", "acosh", "acot", "acoth", "acsc",
        "acsch", "angle", "arg", "asec", "asech", "asin",
        "asinh", "atan", "atanh", "beta", "betainc",
        "betaln", "bincoeff", "cbrt", "ceil", "conj", "cos",
        "cosh", "cot", "coth", "csc", "csch", "erf", "erfc",
        "erfcx", "erfinv", "exp", "finite", "fix", "floor",
        "fmod", "gamma", "gammainc", "gammaln", "imag",
        "isalnum", "isalpha", "isascii", "iscntrl",
        "isdigit", "isfinite", "isgraph", "isinf",
        "islower", "isna", "isnan", "isprint", "ispunct",
        "isspace", "isupper", "isxdigit", "lcm", "lgamma",
        "log", "lower", "mod", "real", "rem", "round",
        "roundb", "sec", "sech", "sign", "sin", "sinh",
        "sqrt", "tan", "tanh", "toascii", "tolower", "xor")

    builtin_consts = (
        "EDITOR", "EXEC_PATH", "I", "IMAGE_PATH", "NA",
        "OCTAVE_HOME", "OCTAVE_VERSION", "PAGER",
        "PAGER_FLAGS", "SEEK_CUR", "SEEK_END", "SEEK_SET",
        "SIG", "S_ISBLK", "S_ISCHR", "S_ISDIR", "S_ISFIFO",
        "S_ISLNK", "S_ISREG", "S_ISSOCK", "WCONTINUE",
        "WCOREDUMP", "WEXITSTATUS", "WIFCONTINUED",
        "WIFEXITED", "WIFSIGNALED", "WIFSTOPPED", "WNOHANG",
        "WSTOPSIG", "WTERMSIG", "WUNTRACED")

    tokens = {
        'root': [
            (r'%\{\s*\n', Comment.Multiline, 'percentblockcomment'),
            (r'#\{\s*\n', Comment.Multiline, 'hashblockcomment'),
            (r'[%#].*$', Comment),
            (r'^\s*function\b', Keyword, 'deffunc'),

            # from 'iskeyword' on hg changeset 8cc154f45e37
            (words((
                '__FILE__', '__LINE__', 'break', 'case', 'catch', 'classdef',
                'continue', 'do', 'else', 'elseif', 'end', 'end_try_catch',
                'end_unwind_protect', 'endclassdef', 'endevents', 'endfor',
                'endfunction', 'endif', 'endmethods', 'endproperties', 'endswitch',
                'endwhile', 'events', 'for', 'function', 'get', 'global', 'if',
                'methods', 'otherwise', 'persistent', 'properties', 'return',
                'set', 'static', 'switch', 'try', 'until', 'unwind_protect',
                'unwind_protect_cleanup', 'while'), suffix=r'\b'),
             Keyword),

            (words(builtin_kw + command_kw + function_kw + loadable_kw + mapping_kw,
                   suffix=r'\b'),  Name.Builtin),

            (words(builtin_consts, suffix=r'\b'), Name.Constant),

            # operators in Octave but not Matlab:
            (r'-=|!=|!|/=|--', Operator),
            # operators:
            (r'-|==|~=|<|>|<=|>=|&&|&|~|\|\|?', Operator),
            # operators in Octave but not Matlab requiring escape for re:
            (r'\*=|\+=|\^=|\/=|\\=|\*\*|\+\+|\.\*\*', Operator),
            # operators requiring escape for re:
            (r'\.\*|\*|\+|\.\^|\^|\.\\|\.\/|\/|\\', Operator),


            # punctuation:
            (r'[\[\](){}:@.,]', Punctuation),
            (r'=|:|;', Punctuation),

            (r'"[^"]*"', String),

            (r'(\d+\.\d*|\d*\.\d+)([eEf][+-]?[0-9]+)?', Number.Float),
            (r'\d+[eEf][+-]?[0-9]+', Number.Float),
            (r'\d+', Number.Integer),

            # quote can be transpose, instead of string:
            # (not great, but handles common cases...)
            (r'(?<=[\w)\].])\'+', Operator),
            (r'(?<![\w)\].])\'', String, 'string'),

            (r'[a-zA-Z_]\w*', Name),
            (r'\s+', Text),
            (r'.', Text),
        ],
        'percentblockcomment': [
            (r'^\s*%\}', Comment.Multiline, '#pop'),
            (r'^.*\n', Comment.Multiline),
            (r'.', Comment.Multiline),
        ],
        'hashblockcomment': [
            (r'^\s*#\}', Comment.Multiline, '#pop'),
            (r'^.*\n', Comment.Multiline),
            (r'.', Comment.Multiline),
        ],
        'string': [
            (r"[^']*'", String, '#pop'),
        ],
        'deffunc': [
            (r'(\s*)(?:(\S+)(\s*)(=)(\s*))?(.+)(\()(.*)(\))(\s*)',
             bygroups(Whitespace, Text, Whitespace, Punctuation,
                      Whitespace, Name.Function, Punctuation, Text,
                      Punctuation, Whitespace), '#pop'),
            # function with no args
            (r'(\s*)([a-zA-Z_]\w*)',
             bygroups(Whitespace, Name.Function), '#pop'),
        ],
    }

    def analyse_text(text):
        """Octave is quite hard to spot, and it looks like Matlab as well."""
        return 0


class ScilabLexer(RegexLexer):
    """
    For Scilab source code.
    """
    name = 'Scilab'
    url = 'https://www.scilab.org/'
    aliases = ['scilab']
    filenames = ['*.sci', '*.sce', '*.tst']
    mimetypes = ['text/scilab']
    version_added = '1.5'

    tokens = {
        'root': [
            (r'//.*?$', Comment.Single),
            (r'^\s*function\b', Keyword, 'deffunc'),

            (words((
                '__FILE__', '__LINE__', 'break', 'case', 'catch', 'classdef', 'continue', 'do', 'else',
                'elseif', 'end', 'end_try_catch', 'end_unwind_protect', 'endclassdef',
                'endevents', 'endfor', 'endfunction', 'endif', 'endmethods', 'endproperties',
                'endswitch', 'endwhile', 'events', 'for', 'function', 'get', 'global', 'if', 'methods',
                'otherwise', 'persistent', 'properties', 'return', 'set', 'static', 'switch', 'try',
                'until', 'unwind_protect', 'unwind_protect_cleanup', 'while'), suffix=r'\b'),
             Keyword),

            (words(_scilab_builtins.functions_kw +
                   _scilab_builtins.commands_kw +
                   _scilab_builtins.macros_kw, suffix=r'\b'), Name.Builtin),

            (words(_scilab_builtins.variables_kw, suffix=r'\b'), Name.Constant),

            # operators:
            (r'-|==|~=|<|>|<=|>=|&&|&|~|\|\|?', Operator),
            # operators requiring escape for re:
            (r'\.\*|\*|\+|\.\^|\^|\.\\|\.\/|\/|\\', Operator),

            # punctuation:
            (r'[\[\](){}@.,=:;]+', Punctuation),

            (r'"[^"]*"', String),

            # quote can be transpose, instead of string:
            # (not great, but handles common cases...)
            (r'(?<=[\w)\].])\'+', Operator),
            (r'(?<![\w)\].])\'', String, 'string'),

            (r'(\d+\.\d*|\d*\.\d+)([eEf][+-]?[0-9]+)?', Number.Float),
            (r'\d+[eEf][+-]?[0-9]+', Number.Float),
            (r'\d+', Number.Integer),

            (r'[a-zA-Z_]\w*', Name),
            (r'\s+', Whitespace),
            (r'.', Text),
        ],
        'string': [
            (r"[^']*'", String, '#pop'),
            (r'.', String, '#pop'),
        ],
        'deffunc': [
            (r'(\s*)(?:(\S+)(\s*)(=)(\s*))?(.+)(\()(.*)(\))(\s*)',
             bygroups(Whitespace, Text, Whitespace, Punctuation,
                      Whitespace, Name.Function, Punctuation, Text,
                      Punctuation, Whitespace), '#pop'),
            # function with no args
            (r'(\s*)([a-zA-Z_]\w*)', bygroups(Text, Name.Function), '#pop'),
        ],
    }

    # the following is needed to distinguish Scilab and GAP .tst files
    def analyse_text(text):
        score = 0.0

        # Scilab comments (don't appear in e.g. GAP code)
        if re.search(r"^\s*//", text):
            score += 0.1
        if re.search(r"^\s*/\*", text):
            score += 0.1

        return min(score, 1.0)

body {
  padding: 0;
  margin: 0;
  width: 100%;
  height: 100%;
}

#root {
  margin: 0;
  padding: 0;
  border: 0;
}
:root {
  --bs-primary: #fc7110 !important;
  --bs-primary-rgb: 255, 87, 51;
  --bs-pagination-active-bg: #ff5733;
  --bs-pagination-active-border: #ff5733;
  --bs-pagination-active-bg: #ff5733;
}

.custom-pagination .page-item.active .page-link {
  background-color: var(--bs-primary) !important;
  border-color: var(--bs-primary) !important;
  color: white !important;
}

.custom-pagination .page-item .page-link {
  color: black;
}

.custom-pagination .page-item .page-link:hover {
  background-color: var(--bs-primary);
  color: white;
}

.product-card{
    transition: ease-in-out 0.3s;
}
.product-card:hover{
    transform: scale(1.02);

}

.btn-quantity{
    width: 2.5rem;
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    background-color: rgb(255, 227, 193) !important;
    border: solid 1px rgb(201, 201, 201) !important;
}

.btn-quantity:hover{
    background-color: var(--bs-primary) !important;
    color: white;
}

table tr:nth-child(odd) td{
    background-color: #f6f1eb;
}

table tr:nth-child(even) td{
  background-color: #e7e7e757;
}

.btn-add, .btn-edit, .btn-delete{
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  border: none !important;
  color: white !important;
  transition: ease-in-out 0.25s;
}

.btn-add{
  background-color: #38a9ff !important;
}

.btn-edit{
  background-color: #ffa938 !important;
}

.btn-delete{
  background-color: #ff3b3b !important;
}

.btn-add:hover {
  background-color: #1f8dd6 !important;
}

.btn-edit:hover {
  background-color: #e68a00 !important;
}

.btn-delete:hover {
  background-color: #d63030 !important;
}
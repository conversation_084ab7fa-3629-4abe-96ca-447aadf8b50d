.appointment {
  max-width: 700px;
  margin: 40px auto;
  padding: 24px 16px;
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 2px 12px rgba(0,0,0,0.07);
}

.appointment__title {
  text-align: center;
  font-size: 2rem;
  font-weight: 600;
  margin-bottom: 32px;
  color: #1a237e;
}

.appointment__toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  margin-bottom: 24px;
  gap: 12px;
}

.appointment__filter-group {
  display: flex;
  gap: 8px;
}

.appointment__filter-btn {
  padding: 6px 18px;
  border: 1px solid #bdbdbd;
  border-radius: 8px;
  background: #f5f5f5;
  color: #333;
  font-weight: 500;
  cursor: pointer;
  transition: background 0.2s, color 0.2s, border 0.2s;
}
.appointment__filter-btn.active {
  background: #1976d2;
  color: #fff;
  border-color: #1976d2;
}
.appointment__filter-btn--warning.active {
  background: #ffb300;
  border-color: #ffb300;
  color: #fff;
}
.appointment__filter-btn--success.active {
  background: #43a047;
  border-color: #43a047;
  color: #fff;
}
.appointment__filter-btn--primary.active {
  background: #1976d2;
  border-color: #1976d2;
  color: #fff;
}
.appointment__filter-btn--danger.active {
  background: #e53935;
  border-color: #e53935;
  color: #fff;
}

.appointment__button {
  padding: 8px 28px;
  background: #1976d2;
  color: #fff;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: background 0.2s;
}
.appointment__button:hover {
  background: #1565c0;
}

.appointment__empty {
  color: #888;
  text-align: center;
  font-size: 1.1rem;
  margin-top: 32px;
}

.appointment__card {
  background: #fafbfc;
  border: 1px solid #e0e0e0;
  border-radius: 12px;
  box-shadow: 0 1px 4px rgba(0,0,0,0.04);
  padding: 20px 18px;
  margin-bottom: 20px;
}

.appointment__card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.appointment__card-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #263238;
  margin: 0;
}

.appointment__badge {
  display: inline-block;
  min-width: 80px;
  text-align: center;
  padding: 6px 0;
  border-radius: 8px;
  font-size: 0.95rem;
  font-weight: 500;
  color: #fff;
}
.appointment__badge--pending {
  background: #ffb300;
  color: #333;
}
.appointment__badge--confirmed {
  background: #43a047;
}
.appointment__badge--rejected {
  background: #1976d2;
}
.appointment__badge--cancelled {
  background: #e53935;
}

.appointment__info {
  margin: 2px 0;
  font-size: 1rem;
  color: #374151;
}

.appointment__action-group {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  margin-top: 16px;
}

.appointment__action-btn {
  padding: 6px 22px;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: background 0.2s;
}
.appointment__action-btn--danger {
  background: #e53935;
  color: #fff;
}
.appointment__action-btn--danger:hover {
  background: #b71c1c;
}
.appointment__action-btn--success {
  background: #43a047;
  color: #fff;
}
.appointment__action-btn--success:hover {
  background: #2e7d32;
}

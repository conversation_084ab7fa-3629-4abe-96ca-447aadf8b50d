import { api } from "./instance";

export const getMedicine = async () => {
    try {
        const response = await api.get(`/medicines/`);
        return response.data;
    } catch (error) {
        return [];
    }
};

export const getMedicineById = async (id) => {
    try {
        const response = await api.get(`/medicines/${id}/`);
        return response.data;
    } catch (error) {
        console.error("Lỗi khi lấy thông tin thuốc:", error);
        return null;
    }
}

export const createMedicine = async (data) => {
    try {
        const response = await api.post("/medicines/", data);
        return response;
    }
    catch (error) {
        return error.response || { status: 500, message: "Lỗi máy chủ" };
    }
};

export const updateMedicine = async (id, data) => {
    const response = await api.put(`/medicines/${id}/`, data);
    return response;
};

export const deleteMedicine = async (id) => {
    try {
        const response = await api.delete(`/medicines/${id}`);
        return response;
    } catch (error) {
        console.error("Lỗi khi xóa thuốc:", error);
        return null;
    }
};
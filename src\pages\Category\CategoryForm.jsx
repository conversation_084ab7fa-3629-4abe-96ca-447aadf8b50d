import React, { useState, useEffect } from "react";
import { useNavigate, useParams } from "react-router-dom"; // Dùng `useNavigate` và `useParams`
import {
  createCategory,
  getCategoryById,
  updateCategory,
} from "../../services/category";

const CategoryForm = () => {
  const [formData, setFormData] = useState({ name: "", description: "" });
  const [message, setMessage] = useState("");
  const { id } = useParams(); // Lấy tham số `id` từ URL
  const navigate = useNavigate(); // Hook dùng để chuyển hướng

  // Nếu có `id` (sửa danh mục), lấy thông tin danh mục
  useEffect(() => {
    if (id) {
      const fetchCategory = async () => {
        try {
          const response = await getCategoryById(id);
          setFormData({
            name: response.name,
            description: response.description,
          });
        } catch (err) {
          setMessage("Lỗi khi lấy thông tin danh mục!");
          console.error(err);
        }
      };
      fetchCategory();
    }
  }, [id]);

  const handleChange = (e) => {
    setFormData((prev) => ({ ...prev, [e.target.name]: e.target.value }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      if (id) {
        const response = await updateCategory(id, formData);
        if (response.status === 200) {
          setMessage("Cập nhật thành công!");
        } else {
          setMessage("Lỗi khi cập nhật!");
        }
      } else {
        const response = await createCategory(formData);
        if (response.status === 201) {
          setMessage("Thêm mới thành công!");
        } else {
          setMessage("Lỗi khi thêm mới!");
        }
      }
      navigate("/category"); // Quay lại trang danh mục sau khi thêm/sửa
    } catch (err) {
      setMessage("Lỗi khi gửi dữ liệu!");
      console.error(err);
    }
  };

  const handleCancel = () => {
    navigate("/category"); // Quay lại trang danh mục nếu huỷ
  };

  return (
    <div className="container my-5">
      <h2 className="text-primary mb-4">
        {id ? "Chỉnh sửa danh mục" : "Thêm danh mục mới"}
      </h2>
      {message && <div className="alert alert-info">{message}</div>}

      <form onSubmit={handleSubmit} className="card p-4 mb-4 shadow-sm">
        <div className="mb-3">
          <label className="form-label">Tên danh mục</label>
          <input
            type="text"
            name="name"
            className="form-control"
            value={formData.name}
            onChange={handleChange}
            required
          />
        </div>

        <div className="mb-3">
          <label className="form-label">Mô tả</label>
          <textarea
            name="description"
            className="form-control"
            rows="2"
            value={formData.description}
            onChange={handleChange}
            required
          />
        </div>

        <div className="d-flex gap-2">
          <button type="submit" className="btn btn-primary">
            {id ? "Cập nhật" : "Thêm"}
          </button>
          <button
            type="button"
            className="btn btn-secondary"
            onClick={handleCancel}
          >
            Huỷ
          </button>
        </div>
      </form>
    </div>
  );
};

export default CategoryForm;

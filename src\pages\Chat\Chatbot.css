/* CSS thuần cho <PERSON>, chu<PERSON><PERSON>EM */
.chatbot {
  max-width: 600px;
  margin: 40px auto;
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 2px 12px rgba(0,0,0,0.07);
  padding: 32px 24px;
}
.chatbot__title {
  text-align: center;
  font-size: 2rem;
  font-weight: 600;
  margin-bottom: 32px;
  color: #1a237e;
}
.chatbot__messages {
  min-height: 300px;
  max-height: 400px;
  overflow-y: auto;
  background: #f5f5f5;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 18px;
}
.chatbot__message {
  margin-bottom: 12px;
  padding: 10px 16px;
  border-radius: 8px;
  background: #e3f2fd;
  color: #1976d2;
  font-size: 1rem;
  max-width: 80%;
}
.chatbot__message--user {
  background: #1976d2;
  color: #fff;
  margin-left: auto;
}
.chatbot__input-group {
  display: flex;
  gap: 8px;
  margin-top: 12px;
}
.chatbot__input {
  flex: 1;
  padding: 10px 12px;
  border: 1px solid #bdbdbd;
  border-radius: 8px;
  font-size: 1rem;
}
.chatbot__button {
  padding: 10px 24px;
  background: #1976d2;
  color: #fff;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  font-size: 1rem;
  cursor: pointer;
}
.chatbot__button:hover {
  background: #1565c0;
}

((*- extends 'display_priority.j2' -*))

%===============================================================================
% Support blocks
%===============================================================================

% Displaying simple data text
((* block data_text *))
    \begin{Verbatim}[commandchars=\\\{\}]
((( output.data['text/plain'] | escape_latex | ansi2latex )))
    \end{Verbatim}
((* endblock data_text *))

% Display python error text with colored frame (saves printer ink vs bkgnd)
((* block error *))
    \begin{Verbatim}[commandchars=\\\{\}, frame=single, framerule=2mm, rulecolor=\color{outerrorbackground}]
(((- super() )))
    \end{Verbatim}
((* endblock error *))
% Display error lines with coloring
((*- block traceback_line *))
((( line | escape_latex | ansi2latex )))
((*- endblock traceback_line *))

% Display stream ouput with coloring
((* block stream *))
    \begin{Verbatim}[commandchars=\\\{\}]
((( output.text | escape_latex | ansi2latex )))
    \end{Verbatim}
((* endblock stream *))

% Display latex
((* block data_latex -*))
    ((( output.data['text/latex'] | strip_files_prefix )))
((* endblock data_latex *))

% Display markdown
((* block data_markdown -*))
    ((( output.data['text/markdown'] | citation2latex | strip_files_prefix | convert_pandoc('markdown+tex_math_double_backslash', 'latex'))))
((* endblock data_markdown *))

% Default mechanism for rendering figures
((*- block data_png -*))((( draw_figure(output.metadata.filenames['image/png']) )))((*- endblock -*))
((*- block data_jpg -*))((( draw_figure(output.metadata.filenames['image/jpeg']) )))((*- endblock -*))
((*- block data_svg -*))((( draw_figure(output.metadata.filenames['image/svg+xml']) )))((*- endblock -*))
((*- block data_pdf -*))((( draw_figure(output.metadata.filenames['application/pdf']) )))((*- endblock -*))

% Draw a figure using the graphicx package.
((* macro draw_figure(filename) -*))
((* set filename = filename | posix_path *))
((*- block figure scoped -*))
    \begin{center}
    \adjustimage{max size={0.9\linewidth}{0.9\paperheight}}{((( filename )))}
    \end{center}
    { \hspace*{\fill} \\}
((*- endblock figure -*))
((*- endmacro *))

% Redirect execute_result to display data priority.
((* block execute_result scoped *))
    ((* block data_priority scoped *))
    ((( super() )))
    ((* endblock *))
((* endblock execute_result *))

% Render markdown
((* block markdowncell scoped *))
    ((( cell.source | citation2latex | strip_files_prefix | convert_pandoc('markdown+tex_math_double_backslash', 'json',extra_args=[]) | resolve_references | convert_explicitly_relative_paths | convert_pandoc('json','latex'))))
((* endblock markdowncell *))

% Don't display unknown types
((* block unknowncell scoped *))
((* endblock unknowncell *))

import { BrowserRouter, Routes, Route } from "react-router-dom";
import Layout from "./layouts/layout.jsx";
import Login from "./pages/Login/Login.jsx";
import Register from "./pages/Register/Register.jsx";
import Home from "./pages/Home/Home.jsx";
import ProfilePage from "./pages/Profile/Profile.jsx";
import AppointmentAdd from "./pages/Appointment/AppointmentAdd.jsx";
import AppointmentHistory from "./pages/Appointment/Appointment.jsx";
import Availability from "./pages/Availability/Availability.jsx";
import PrescriptionForm from "./pages/Prescription/PrescriptionAdd.jsx";
import PrescriptionList from "./pages/Prescription/Prescription.jsx";
import CategoryManager from "./pages/Category/Category.jsx";
import CategoryForm from "./pages/Category/CategoryForm.jsx";
import MedicineManager from "./pages/Medicine/Medicine.jsx";
import MedicineForm from "./pages/Medicine/MedicineForm.jsx";
import LabForm from "./pages/Lab/LabAdd.jsx";
import LabList from "./pages/Lab/Lab.jsx";
import LabResult from "./pages/Lab/LabResult.jsx";
import RecordForm from "./pages/Record/RecordAdd.jsx";
import RecordList from "./pages/Record/Record.jsx";
import Chatbot from "./pages/Chat/Chatbot.jsx";

function App() {
  return (
    <BrowserRouter>
      <Routes>
        <Route path="/" element={<Layout />}>
          <Route path="/" element={<Home />}></Route> // trang home
          <Route path="/profile" element={<ProfilePage />}></Route> // trang
          profile
          <Route path="/appointment/add" element={<AppointmentAdd />} /> //
          trang thêm lịch hẹn
          <Route path="/appointment" element={<AppointmentHistory />} /> //
          trang lịch sử hẹn khám
          <Route path="/available" element={<Availability />} /> // trang hiển
          thị lịch khám
          <Route path="/prescription" element={<PrescriptionList />} /> // trang
          danh sách đơn thuốc
          <Route path="/prescription/add" element={<PrescriptionForm />} /> //
          trang thêm đơn thuốc
          <Route path="/category" element={<CategoryManager />} /> // Trang quản
          lý danh mục thuốc
          <Route path="/category/add" element={<CategoryForm />} />{" "}
          {/* Route thêm mới */}
          <Route path="/category/edit/:id" element={<CategoryForm />} />{" "}
          {/* Route chỉnh sửa */}
          <Route path="/medicine" element={<MedicineManager />} /> // Trang quản
          lý thuốc
          <Route path="/medicine/add" element={<MedicineForm />} /> // Route
          thêm mới thuốc
          <Route path="/medicine/edit/:id" element={<MedicineForm />} /> //
          Route chỉnh sửa thuốc
          <Route path="/lab" element={<LabList />} /> // Trang quản lý xét
          nghiệm
          <Route path="/lab/add" element={<LabForm />} />
          // Route thêm mới xét nghiệm
          <Route path="/lab/result/:id" element={<LabResult />} />
          // Route xem kết quả xét nghiệm
          <Route path="/record" element={<RecordList />} /> // Trang quản lý hồ
          sơ bệnh án
          <Route path="/record/add" element={<RecordForm />} />
          // Route thêm mới hồ sơ bệnh án
          <Route path="/chat" element={<Chatbot />} /> // Trang chatbot hỗ trợ
          bệnh nhân
        </Route>
        <Route path="/login" element={<Login />}></Route>
        <Route path="/register" element={<Register />}></Route>
      </Routes>
    </BrowserRouter>
  );
}

export default App;

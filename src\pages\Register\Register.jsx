import React, { useState } from "react";
import { registerService } from "../../services/user.jsx";
import { Link, useNavigate } from "react-router-dom";
import "./Register.css";

const Register = () => {
  const navigate = useNavigate();
  const [formData, setFormData] = useState({
    first_name: "",
    last_name: "",
    email: "",
    password: "",
    confirmPassword: "",
  });
  const [message, setMessage] = useState("");

  // Hàm xử lý khi nhập liệu vào form
  const handleChange = (e) => {
    setFormData({ ...formData, [e.target.name]: e.target.value });
  };

  // Xử lý đăng ký
  const handleSubmit = async (e) => {
    e.preventDefault();
    const { first_name, last_name, email, password, confirmPassword } =
      formData;

    if (!first_name) return setMessage("<PERSON>ọ tên không được để trống");
    if (!last_name) return setMessage("Tên không được để trống");
    if (!email) return setMessage("Email không được để trống");
    if (!password) return setMessage("Mật khẩu không được để trống");
    if (!confirmPassword)
      return setMessage("Xác nhận mật khẩu không được để trống");
    if (password !== confirmPassword)
      return setMessage("Mật khẩu không trùng khớp");

    try {
      const response = await registerService({
        first_name,
        last_name,
        email,
        password,
        role: "PATIENT",
      });
      if (response.status === 200) {
        navigate("/login");
      } else {
        console.log(response.data.error);
        setMessage(
          response?.data?.error ||
            response?.data?.message ||
            "Có lỗi xảy ra, vui lòng thử lại!"
        );
      }
    } catch (error) {
      setMessage("Có lỗi xảy ra, vui lòng thử lại!");
    }
  };

  return (
    <div className="register">
      <h2 className="register__title">Đăng ký</h2>
      {message && <div className="register__error">{message}</div>}
      <form onSubmit={handleSubmit} className="register__form">
        <div className="register__group">
          <label className="register__label">Họ</label>
          <input
            type="text"
            className="register__input"
            name="first_name"
            placeholder="Nhập họ"
            value={formData.first_name}
            onChange={handleChange}
            required
          />
        </div>
        <div className="register__group">
          <label className="register__label">Tên</label>
          <input
            type="text"
            className="register__input"
            name="last_name"
            placeholder="Nhập tên"
            value={formData.last_name}
            onChange={handleChange}
            required
          />
        </div>
        <div className="register__group">
          <label className="register__label">Email</label>
          <input
            type="email"
            className="register__input"
            name="email"
            placeholder="Nhập email"
            value={formData.email}
            onChange={handleChange}
            required
          />
        </div>
        <div className="register__group">
          <label className="register__label">Mật khẩu</label>
          <input
            type="password"
            className="register__input"
            name="password"
            placeholder="Nhập mật khẩu"
            value={formData.password}
            onChange={handleChange}
            required
          />
        </div>
        <div className="register__group">
          <label className="register__label">Xác nhận mật khẩu</label>
          <input
            type="password"
            className="register__input"
            name="confirmPassword"
            placeholder="Xác nhận mật khẩu"
            value={formData.confirmPassword}
            onChange={handleChange}
            required
          />
        </div>
        <button type="submit" className="register__button">
          Đăng ký
        </button>
      </form>
      <div className="register__footer">
        <Link to="/login" className="register__link">
          Bạn đã có tài khoản? Đăng nhập!
        </Link>
      </div>
      <div className="register__footer">
        <Link to="/" className="register__button register__button--outline">
          Quay lại Trang chủ
        </Link>
      </div>
    </div>
  );
};

export default Register;

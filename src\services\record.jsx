import { api } from "./instance";

export const getRecord = async () => {
    try {
        const response = await api.get(`/records/`);
        return response.data;
    } catch (error) {
        return [];
    }
};

export const getRecordById = async (id) => {
    try {
        const response = await api.get(`/records/${id}/`);
        return response.data;
    } catch (error) {
        console.error("Lỗi khi lấy thông tin thuốc:", error);
        return null;
    }
}

export const createRecord = async (data) => {
    try {
        const response = await api.post("/records/", data);
        return response;
    }
    catch (error) {
        return error.response || { status: 500, message: "Lỗi máy chủ" };
    }
};

export const updateRecord = async (id, data) => {
    const response = await api.put(`/records/${id}/`, data);
    return response;
};

export const deleteRecord = async (id) => {
    try {
        const response = await api.delete(`/records/${id}`);
        return response;
    } catch (error) {
        console.error("Lỗi khi xóa thuốc:", error);
        return null;
    }
};
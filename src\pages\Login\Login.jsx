import React, { useState } from "react";
import { Link, useNavigate } from "react-router-dom";
import { loginService } from "../../services/user.jsx";
import "./Login.css";

const Login = () => {
  const navigate = useNavigate();
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [message, setMessage] = useState("");

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!email) return setMessage("Email không được để trống");
    if (!password) return setMessage("Mật khẩu không được để trống");

    const response = await loginService({ email, password });
    if (response.status === 200) {
      navigate("/");
    } else setMessage(response.message);
  };

  return (
    <div className="login">
      <div className="login__container">
        <h2 className="login__title">Đ<PERSON>ng nhập</h2>
        {message && <div className="login__error">{message}</div>}
        <form onSubmit={handleSubmit} className="login__form">
          <div className="login__group">
            <label className="login__label">Email</label>
            <input
              type="email"
              className="login__input"
              placeholder="Nhập email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              required
            />
          </div>
          <div className="login__group">
            <label className="login__label">Mật khẩu</label>
            <input
              type="password"
              className="login__input"
              placeholder="Nhập mật khẩu"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              required
            />
          </div>
          <button type="submit" className="login__button">
            Đăng nhập
          </button>
        </form>
        <div className="login__footer">
          <Link to="/register" className="login__link">
            Bạn chưa có tài khoản? Đăng ký ngay!
          </Link>
        </div>
        <div className="login__footer">
          <Link to="/" className="login__button login__button--outline">
            Quay lại Trang chủ
          </Link>
        </div>
      </div>
    </div>
  );
};

export default Login;

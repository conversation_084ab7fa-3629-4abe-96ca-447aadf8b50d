import { api } from "./instance";

export const getPrescription = async () => {
    try {
        const response = await api.get(`/prescriptions/`);
        return response.data;
    } catch (error) {
        return [];
    }
};

export const getPrescriptionByStatus = async (status) => {
    try {
        const response = await api.get(`/prescriptions/?status=${status}`);
        return response.data;
    } catch (error) {
        return [];
    }
};

export const createPrescription = async (data) => {
    try {
        const response = await api.post("/prescriptions/", data);
        return response;
    }
    catch (error) {
        return error.response || { status: 500, message: "Lỗi máy chủ" };
    }
};

export const updatePrescription = async (id, data) => {
    const response = await api.put(`/prescriptions/${id}/`, data);
    return response;
};
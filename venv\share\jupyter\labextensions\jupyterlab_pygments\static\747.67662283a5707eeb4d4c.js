"use strict";(self.webpackChunkjupyterlab_pygments=self.webpackChunkjupyterlab_pygments||[]).push([[747],{150:(r,o,t)=>{t.d(o,{Z:()=>c});var e=t(81),i=t.n(e),n=t(645),l=t.n(n)()(i());l.push([r.id,"\n/*-----------------------------------------------------------------------------\n| Copyright (c) Jupyter Development Team.\n| Distributed under the terms of the Modified BSD License.\n|----------------------------------------------------------------------------*/\n/* This file was auto-generated by generate_css.py in jupyterlab_pygments */\n\n.highlight .hll { background-color: var(--jp-cell-editor-active-background) }\n.highlight { background: var(--jp-cell-editor-background); color: var(--jp-mirror-editor-variable-color) }\n.highlight .c { color: var(--jp-mirror-editor-comment-color); font-style: italic } /* Comment */\n.highlight .err { color: var(--jp-mirror-editor-error-color) } /* Error */\n.highlight .k { color: var(--jp-mirror-editor-keyword-color); font-weight: bold } /* Keyword */\n.highlight .o { color: var(--jp-mirror-editor-operator-color); font-weight: bold } /* Operator */\n.highlight .p { color: var(--jp-mirror-editor-punctuation-color) } /* Punctuation */\n.highlight .ch { color: var(--jp-mirror-editor-comment-color); font-style: italic } /* Comment.Hashbang */\n.highlight .cm { color: var(--jp-mirror-editor-comment-color); font-style: italic } /* Comment.Multiline */\n.highlight .cp { color: var(--jp-mirror-editor-comment-color); font-style: italic } /* Comment.Preproc */\n.highlight .cpf { color: var(--jp-mirror-editor-comment-color); font-style: italic } /* Comment.PreprocFile */\n.highlight .c1 { color: var(--jp-mirror-editor-comment-color); font-style: italic } /* Comment.Single */\n.highlight .cs { color: var(--jp-mirror-editor-comment-color); font-style: italic } /* Comment.Special */\n.highlight .kc { color: var(--jp-mirror-editor-keyword-color); font-weight: bold } /* Keyword.Constant */\n.highlight .kd { color: var(--jp-mirror-editor-keyword-color); font-weight: bold } /* Keyword.Declaration */\n.highlight .kn { color: var(--jp-mirror-editor-keyword-color); font-weight: bold } /* Keyword.Namespace */\n.highlight .kp { color: var(--jp-mirror-editor-keyword-color); font-weight: bold } /* Keyword.Pseudo */\n.highlight .kr { color: var(--jp-mirror-editor-keyword-color); font-weight: bold } /* Keyword.Reserved */\n.highlight .kt { color: var(--jp-mirror-editor-keyword-color); font-weight: bold } /* Keyword.Type */\n.highlight .m { color: var(--jp-mirror-editor-number-color) } /* Literal.Number */\n.highlight .s { color: var(--jp-mirror-editor-string-color) } /* Literal.String */\n.highlight .ow { color: var(--jp-mirror-editor-operator-color); font-weight: bold } /* Operator.Word */\n.highlight .pm { color: var(--jp-mirror-editor-punctuation-color) } /* Punctuation.Marker */\n.highlight .w { color: var(--jp-mirror-editor-variable-color) } /* Text.Whitespace */\n.highlight .mb { color: var(--jp-mirror-editor-number-color) } /* Literal.Number.Bin */\n.highlight .mf { color: var(--jp-mirror-editor-number-color) } /* Literal.Number.Float */\n.highlight .mh { color: var(--jp-mirror-editor-number-color) } /* Literal.Number.Hex */\n.highlight .mi { color: var(--jp-mirror-editor-number-color) } /* Literal.Number.Integer */\n.highlight .mo { color: var(--jp-mirror-editor-number-color) } /* Literal.Number.Oct */\n.highlight .sa { color: var(--jp-mirror-editor-string-color) } /* Literal.String.Affix */\n.highlight .sb { color: var(--jp-mirror-editor-string-color) } /* Literal.String.Backtick */\n.highlight .sc { color: var(--jp-mirror-editor-string-color) } /* Literal.String.Char */\n.highlight .dl { color: var(--jp-mirror-editor-string-color) } /* Literal.String.Delimiter */\n.highlight .sd { color: var(--jp-mirror-editor-string-color) } /* Literal.String.Doc */\n.highlight .s2 { color: var(--jp-mirror-editor-string-color) } /* Literal.String.Double */\n.highlight .se { color: var(--jp-mirror-editor-string-color) } /* Literal.String.Escape */\n.highlight .sh { color: var(--jp-mirror-editor-string-color) } /* Literal.String.Heredoc */\n.highlight .si { color: var(--jp-mirror-editor-string-color) } /* Literal.String.Interpol */\n.highlight .sx { color: var(--jp-mirror-editor-string-color) } /* Literal.String.Other */\n.highlight .sr { color: var(--jp-mirror-editor-string-color) } /* Literal.String.Regex */\n.highlight .s1 { color: var(--jp-mirror-editor-string-color) } /* Literal.String.Single */\n.highlight .ss { color: var(--jp-mirror-editor-string-color) } /* Literal.String.Symbol */\n.highlight .il { color: var(--jp-mirror-editor-number-color) } /* Literal.Number.Integer.Long */",""]);const c=l},645:r=>{r.exports=function(r){var o=[];return o.toString=function(){return this.map((function(o){var t="",e=void 0!==o[5];return o[4]&&(t+="@supports (".concat(o[4],") {")),o[2]&&(t+="@media ".concat(o[2]," {")),e&&(t+="@layer".concat(o[5].length>0?" ".concat(o[5]):""," {")),t+=r(o),e&&(t+="}"),o[2]&&(t+="}"),o[4]&&(t+="}"),t})).join("")},o.i=function(r,t,e,i,n){"string"==typeof r&&(r=[[null,r,void 0]]);var l={};if(e)for(var c=0;c<this.length;c++){var a=this[c][0];null!=a&&(l[a]=!0)}for(var h=0;h<r.length;h++){var g=[].concat(r[h]);e&&l[g[0]]||(void 0!==n&&(void 0===g[5]||(g[1]="@layer".concat(g[5].length>0?" ".concat(g[5]):""," {").concat(g[1],"}")),g[5]=n),t&&(g[2]?(g[1]="@media ".concat(g[2]," {").concat(g[1],"}"),g[2]=t):g[2]=t),i&&(g[4]?(g[1]="@supports (".concat(g[4],") {").concat(g[1],"}"),g[4]=i):g[4]="".concat(i)),o.push(g))}},o}},81:r=>{r.exports=function(r){return r[1]}},379:r=>{var o=[];function t(r){for(var t=-1,e=0;e<o.length;e++)if(o[e].identifier===r){t=e;break}return t}function e(r,e){for(var n={},l=[],c=0;c<r.length;c++){var a=r[c],h=e.base?a[0]+e.base:a[0],g=n[h]||0,s="".concat(h," ").concat(g);n[h]=g+1;var d=t(s),p={css:a[1],media:a[2],sourceMap:a[3],supports:a[4],layer:a[5]};if(-1!==d)o[d].references++,o[d].updater(p);else{var m=i(p,e);e.byIndex=c,o.splice(c,0,{identifier:s,updater:m,references:1})}l.push(s)}return l}function i(r,o){var t=o.domAPI(o);return t.update(r),function(o){if(o){if(o.css===r.css&&o.media===r.media&&o.sourceMap===r.sourceMap&&o.supports===r.supports&&o.layer===r.layer)return;t.update(r=o)}else t.remove()}}r.exports=function(r,i){var n=e(r=r||[],i=i||{});return function(r){r=r||[];for(var l=0;l<n.length;l++){var c=t(n[l]);o[c].references--}for(var a=e(r,i),h=0;h<n.length;h++){var g=t(n[h]);0===o[g].references&&(o[g].updater(),o.splice(g,1))}n=a}}},569:r=>{var o={};r.exports=function(r,t){var e=function(r){if(void 0===o[r]){var t=document.querySelector(r);if(window.HTMLIFrameElement&&t instanceof window.HTMLIFrameElement)try{t=t.contentDocument.head}catch(r){t=null}o[r]=t}return o[r]}(r);if(!e)throw new Error("Couldn't find a style target. This probably means that the value for the 'insert' parameter is invalid.");e.appendChild(t)}},216:r=>{r.exports=function(r){var o=document.createElement("style");return r.setAttributes(o,r.attributes),r.insert(o,r.options),o}},565:(r,o,t)=>{r.exports=function(r){var o=t.nc;o&&r.setAttribute("nonce",o)}},795:r=>{r.exports=function(r){if("undefined"==typeof document)return{update:function(){},remove:function(){}};var o=r.insertStyleElement(r);return{update:function(t){!function(r,o,t){var e="";t.supports&&(e+="@supports (".concat(t.supports,") {")),t.media&&(e+="@media ".concat(t.media," {"));var i=void 0!==t.layer;i&&(e+="@layer".concat(t.layer.length>0?" ".concat(t.layer):""," {")),e+=t.css,i&&(e+="}"),t.media&&(e+="}"),t.supports&&(e+="}");var n=t.sourceMap;n&&"undefined"!=typeof btoa&&(e+="\n/*# sourceMappingURL=data:application/json;base64,".concat(btoa(unescape(encodeURIComponent(JSON.stringify(n))))," */")),o.styleTagTransform(e,r,o.options)}(o,r,t)},remove:function(){!function(r){if(null===r.parentNode)return!1;r.parentNode.removeChild(r)}(o)}}}},589:r=>{r.exports=function(r,o){if(o.styleSheet)o.styleSheet.cssText=r;else{for(;o.firstChild;)o.removeChild(o.firstChild);o.appendChild(document.createTextNode(r))}}},747:(r,o,t)=>{t.r(o);var e=t(379),i=t.n(e),n=t(795),l=t.n(n),c=t(569),a=t.n(c),h=t(565),g=t.n(h),s=t(216),d=t.n(s),p=t(589),m=t.n(p),u=t(150),v={};v.styleTagTransform=m(),v.setAttributes=g(),v.insert=a().bind(null,"head"),v.domAPI=l(),v.insertStyleElement=d(),i()(u.Z,v),u.Z&&u.Z.locals&&u.Z.locals}}]);
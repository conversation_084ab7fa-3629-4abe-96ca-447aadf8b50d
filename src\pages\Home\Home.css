/* CSS thuần cho Home, chuẩn BEM */
.home {
  max-width: 1000px;
  margin: 40px auto;
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 2px 12px rgba(0,0,0,0.07);
  padding: 32px 24px;
}
.home__title {
  text-align: center;
  font-size: 2.2rem;
  font-weight: 700;
  margin-bottom: 32px;
  color: #1976d2;
}
.home__section {
  margin-bottom: 32px;
}
.home__card-list {
  display: flex;
  gap: 24px;
  flex-wrap: wrap;
  justify-content: center;
}
.home__card {
  background: #f5f5f5;
  border-radius: 12px;
  box-shadow: 0 1px 4px rgba(0,0,0,0.04);
  padding: 24px 18px;
  min-width: 220px;
  max-width: 320px;
  flex: 1 1 220px;
  text-align: center;
}
.home__card-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: #263238;
  margin-bottom: 8px;
}
.home__card-text {
  color: #374151;
  font-size: 1rem;
}

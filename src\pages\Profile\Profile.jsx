import React, { useState, useEffect } from "react";
import { Container, Row, Col, Card, ListGroup, Spinner } from "react-bootstrap";
import { myProfile } from "../../services/user";

const ProfilePage = () => {
  const [user, setUser] = useState(null);

  useEffect(() => {
    const fetchProfile = async () => {
      try {
        const data = await myProfile();
        setUser(data);
      } catch (error) {
        console.error("Failed to fetch profile:", error);
      }
    };
    fetchProfile();
  }, []);

  return (
    <Container className="my-5">
      <Row className="justify-content-center">
        <Col md={8} lg={6}>
          <Card className="shadow border-0">
            <Card.Header
              className="text-white text-center py-3 rounded-top"
              style={{ backgroundColor: "var(--bs-primary)" }}
            >
              <h4 className="mb-0">Thông tin cá nhân</h4>
            </Card.Header>
            {user ? (
              <Card.Body>
                <Row>
                  <Col xs={12} className="mb-3">
                    <strong>Họ và tên:</strong> {user.last_name}{" "}
                    {user.first_name}
                  </Col>
                  <Col xs={12} className="mb-3">
                    <strong>Email:</strong> {user.email}
                  </Col>
                </Row>

                <h5
                  className="mt-4 mb-1"
                  style={{ color: "var(--bs-primary)" }}
                >
                  Địa chỉ
                </h5>
                <ListGroup variant="flush">
                  <ListGroup.Item>
                    <strong>Số nhà:</strong> {user.address.house_number}
                  </ListGroup.Item>
                  <ListGroup.Item>
                    <strong>Đường:</strong> {user.address.street}
                  </ListGroup.Item>
                  <ListGroup.Item>
                    <strong>Phường:</strong> {user.address.ward}
                  </ListGroup.Item>
                  <ListGroup.Item>
                    <strong>Quận:</strong> {user.address.district}
                  </ListGroup.Item>
                  <ListGroup.Item>
                    <strong>Tỉnh/TP:</strong> {user.address.province}
                  </ListGroup.Item>
                </ListGroup>
              </Card.Body>
            ) : (
              <Card.Body className="text-center">
                <Spinner animation="border" variant="primary" />
                <p className="mt-2">Đang tải thông tin...</p>
              </Card.Body>
            )}
          </Card>
        </Col>
      </Row>
    </Container>
  );
};

export default ProfilePage;

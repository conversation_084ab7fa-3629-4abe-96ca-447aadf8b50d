{"cells": [{"cell_type": "code", "execution_count": null, "id": "eb5586f8", "metadata": {}, "outputs": [], "source": ["import sys\n", "import wasabi\n", "\n", "wasabi.msg.warn(\"This is a test. This is only a test.\")\n", "if sys.version_info >= (3, 7):\n", "    assert wasabi.util.supports_ansi()\n", "\n", "print(sys.stdout)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.7"}}, "nbformat": 4, "nbformat_minor": 5}
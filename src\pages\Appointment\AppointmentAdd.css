/* CSS thuần cho Appointment<PERSON>dd, chu<PERSON><PERSON> BEM */
.appointment-add {
  max-width: 700px;
  margin: 40px auto;
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 2px 12px rgba(0,0,0,0.07);
  padding: 32px 24px;
}
.appointment-add__title {
  text-align: center;
  font-size: 2rem;
  font-weight: 600;
  margin-bottom: 32px;
  color: #1a237e;
}
.appointment-add__form {
  margin-top: 24px;
}
.appointment-add__row {
  display: flex;
  gap: 16px;
  margin-bottom: 18px;
}
.appointment-add__label {
  font-weight: 500;
  color: #374151;
  margin-bottom: 6px;
}
.appointment-add__select,
.appointment-add__input {
  padding: 10px 12px;
  border: 1px solid #bdbdbd;
  border-radius: 8px;
  font-size: 1rem;
  width: 100%;
}
.appointment-add__table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 18px;
}
.appointment-add__table th,
.appointment-add__table td {
  border: 1px solid #e0e0e0;
  padding: 10px 8px;
  text-align: center;
}
.appointment-add__table th {
  background: #f5f5f5;
  font-weight: 600;
}
.appointment-add__button {
  padding: 8px 24px;
  background: #1976d2;
  color: #fff;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  margin-top: 18px;
}
.appointment-add__button:hover {
  background: #1565c0;
}
.appointment-add__note {
  color: #e53935;
  font-size: 0.95rem;
  margin-bottom: 8px;
  text-align: center;
}

import { api } from "./instance";

export const getLab = async () => {
    try {
        const response = await api.get(`/labs/`);
        return response.data;
    } catch (error) {
        return [];
    }
};

export const getLabByStatus = async (status) => {
    try {
        const response = await api.get(`/labs/?status=${status}`);
        return response.data;
    } catch (error) {
        return [];
    }
}

export const getLabById = async (id) => {
    try {
        const response = await api.get(`/labs/${id}/`);
        return response.data;
    } catch (error) {
        console.error("Lỗi khi lấy thông tin thuốc:", error);
        return null;
    }
}

export const createLab = async (data) => {
    try {
        const response = await api.post("/labs/", data);
        return response;
    }
    catch (error) {
        return error.response || { status: 500, message: "Lỗi máy chủ" };
    }
};

export const updateLab = async (id, data) => {
    const response = await api.put(`/labs/result/${id}/`, data);
    return response;
};

export const deleteLab = async (id) => {
    try {
        const response = await api.delete(`/labs/${id}/`);
        return response;
    } catch (error) {
        console.error("Lỗi khi xóa thuốc:", error);
        return null;
    }
};
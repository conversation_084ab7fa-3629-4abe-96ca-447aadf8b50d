import { api } from "./instance";

export const getAppointmentBooked = async (id, start_date, end_date, detail = false) => {
    try {
        const response = await api.get(`/appointments/booked/${id}`, {
            params: {
                start_date,
                end_date,
                detail
            }
        });
        return response.data;
    } catch (error) {
        console.error("Lỗi khi lấy lịch hẹn:", error);
        return [];
    }
}

export const getAppointment = async (status) => {
    try {
        const response = await api.get(`/appointments?status=${status}`);
        return response.data;
    } catch (error) {
        console.error("Lỗi khi lấy lịch hẹn:", error);
        return [];
    }
};

export const createAppointment = async (data) => {
    try {
        const response = await api.post("/appointments/", data);
        return response;
    }
    catch (error) {
        console.error("Lỗi khi tạo lịch hẹn:", error);
        return error.response || { status: 500, message: "Lỗi máy chủ" };
    }
};

export const updateAppointment = async (id, status) => {
    const response = await api.put(`/appointments/${id}/`,{status: status});
    return response;
};

export const getShift = async () => {
    const response = await api.get("/shifts/");
    return response.data;
}

export const getDoctortAvailable = async (doctor_id) => {
    const response = await api.get(`/doctorAvailability?doctor_id=${doctor_id}`);
    return response.data;
}
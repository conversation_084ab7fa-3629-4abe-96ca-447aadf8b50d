/* CSS thuần cho Record, chu<PERSON>n BEM */
.record {
  max-width: 700px;
  margin: 40px auto;
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 2px 12px rgba(0,0,0,0.07);
  padding: 24px 16px;
}
.record__title {
  text-align: center;
  font-size: 2rem;
  font-weight: 600;
  margin-bottom: 32px;
  color: #1a237e;
}
.record__list {
  margin-top: 24px;
}
.record__item {
  background: #fafbfc;
  border: 1px solid #e0e0e0;
  border-radius: 12px;
  box-shadow: 0 1px 4px rgba(0,0,0,0.04);
  padding: 18px 16px;
  margin-bottom: 18px;
}
.record__item-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #263238;
  margin-bottom: 6px;
}
.record__info {
  font-size: 1rem;
  color: #374151;
  margin: 2px 0;
}
.record__add-btn {
  padding: 8px 24px;
  background: #1976d2;
  color: #fff;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  margin-bottom: 18px;
}
.record__add-btn:hover {
  background: #1565c0;
}

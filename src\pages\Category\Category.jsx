import React, { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom"; // Import hook để chuyển hướng
import { getCategory, deleteCategory } from "../../services/category";

const CategoryManager = () => {
  const [categories, setCategories] = useState([]);
  const [message, setMessage] = useState("");
  const navigate = useNavigate(); // Hook dùng để chuyển hướng

  const fetchCategories = async () => {
    try {
      const response = await getCategory();
      setCategories(response);
    } catch (err) {
      console.error("Lỗi khi lấy danh mục:", err);
    }
  };

  useEffect(() => {
    fetchCategories();
  }, []);

  const handleDelete = async (id) => {
    if (!window.confirm("Bạn có chắc muốn xoá?")) return;
    try {
      const response = await deleteCategory(id);
      if (response.status === 204) {
        setMessage("Xoá thành công!");
        fetchCategories();
      } else {
        setMessage("Lỗi khi xoá!");
      }
    } catch (err) {
      setMessage("Lỗi khi xoá!");
      console.error(err);
    }
  };

  const handleEdit = (category) => {
    navigate(`/category/edit/${category.id}`); // Chuyển hướng sang trang sửa
  };

  const handleAdd = () => {
    navigate("/category/add"); // Chuyển hướng sang trang thêm mới
  };

  return (
    <div className="container my-5">
      <h2 className="text-primary mb-4">Quản lý Danh mục (Category)</h2>
      {message && <div className="alert alert-info">{message}</div>}

      <button className="btn btn-success mb-3" onClick={handleAdd}>
        Thêm danh mục mới
      </button>

      <table className="table table-bordered table-hover">
        <thead className="table-light">
          <tr>
            <th>#</th>
            <th>Tên</th>
            <th>Mô tả</th>
            <th>Hành động</th>
          </tr>
        </thead>
        <tbody>
          {categories.map((cat, index) => (
            <tr key={cat.id}>
              <td>{index + 1}</td>
              <td>{cat.name}</td>
              <td>{cat.description}</td>
              <td>
                <button
                  className="btn btn-warning btn-sm me-2"
                  onClick={() => handleEdit(cat)}
                >
                  Sửa
                </button>
                <button
                  className="btn btn-danger btn-sm"
                  onClick={() => handleDelete(cat.id)}
                >
                  Xoá
                </button>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default CategoryManager;

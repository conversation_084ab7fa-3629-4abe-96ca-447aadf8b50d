/* CSS thuần cho Availability, chuẩn BEM */
.availability {
  max-width: 800px;
  margin: 40px auto;
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 2px 12px rgba(0,0,0,0.07);
  padding: 32px 24px;
}
.availability__title {
  text-align: center;
  font-size: 2rem;
  font-weight: 600;
  margin-bottom: 32px;
  color: #1a237e;
}
.availability__toolbar {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 24px;
  gap: 12px;
}
.availability__table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 18px;
}
.availability__table th,
.availability__table td {
  border: 1px solid #e0e0e0;
  padding: 10px 8px;
  text-align: center;
}
.availability__table th {
  background: #f5f5f5;
  font-weight: 600;
}
.availability__button {
  padding: 8px 24px;
  background: #1976d2;
  color: #fff;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
}
.availability__button:hover {
  background: #1565c0;
}

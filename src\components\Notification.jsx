import React, { useEffect, useState } from "react";
import { Modal, Button, Toast, ToastContainer  } from "react-bootstrap";

export const PopupNotification = ({show, message, type = "info", onClose, action, actionTitle}) => {
  return (
    <Modal show={show} onHide={onClose} centered>
      <Modal.Header closeButton>
        <Modal.Title className={`text-${type}`}>{type.toUpperCase()}</Modal.Title>
      </Modal.Header>
      <Modal.Body>{message}</Modal.Body>
      <Modal.Footer style={{justifyContent: "center", gap: "1rem"}}>
        {action && (
          <Button style={{backgroundColor: "var(--bs-danger)", border: "none", minWidth: "5rem"}} onClick={action}>
            {actionTitle}
          </Button>
        )}
      </Modal.Footer>
    </Modal>
  );
};

export const SideNotification = ({ show, message, type = "info", onClose }) => {
    const [visible, setVisible] = useState(show);
    useEffect(() => {
        setVisible(show);
        if (show) {
        const timer = setTimeout(() => {
            setVisible(false);
            onClose();
        }, 3000);
        return () => clearTimeout(timer);
        }
    }, [show, onClose]);

    if (!message) return null; // Không hiển thị nếu không có thông báo

    return (
        <ToastContainer className="p-3" style={{ zIndex: 1050, position: "fixed", bottom: "20px", right: "20px" }}>
        <Toast bg={type} show={visible} onClose={() => setShow(false)} delay={3000} autohide>
            <Toast.Header closeButton={false}>
            <strong className="me-auto">{type.toUpperCase()}</strong>
            <small>just now</small>
            </Toast.Header>
            <Toast.Body className="text-white">{message}</Toast.Body>
        </Toast>
        </ToastContainer>
    );
};



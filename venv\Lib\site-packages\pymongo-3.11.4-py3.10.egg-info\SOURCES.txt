LICENSE
MANIFEST.in
README.rst
THIRD-PARTY-NOTICES
ez_setup.py
setup.cfg
setup.py
bson/__init__.py
bson/_cbsonmodule.c
bson/_cbsonmodule.h
bson/binary.py
bson/bson-endian.h
bson/bson-stdint-win32.h
bson/buffer.c
bson/buffer.h
bson/code.py
bson/codec_options.py
bson/dbref.py
bson/decimal128.py
bson/encoding_helpers.c
bson/encoding_helpers.h
bson/errors.py
bson/int64.py
bson/json_util.py
bson/max_key.py
bson/min_key.py
bson/objectid.py
bson/py3compat.py
bson/raw_bson.py
bson/regex.py
bson/son.py
bson/time64.c
bson/time64.h
bson/time64_config.h
bson/time64_limits.h
bson/timestamp.py
bson/tz_util.py
doc/__init__.py
doc/atlas.rst
doc/changelog.rst
doc/compatibility-policy.rst
doc/conf.py
doc/contributors.rst
doc/faq.rst
doc/index.rst
doc/installation.rst
doc/migrate-to-pymongo3.rst
doc/mongo_extensions.py
doc/python3.rst
doc/tools.rst
doc/tutorial.rst
doc/api/index.rst
doc/api/bson/binary.rst
doc/api/bson/code.rst
doc/api/bson/codec_options.rst
doc/api/bson/dbref.rst
doc/api/bson/decimal128.rst
doc/api/bson/errors.rst
doc/api/bson/index.rst
doc/api/bson/int64.rst
doc/api/bson/json_util.rst
doc/api/bson/max_key.rst
doc/api/bson/min_key.rst
doc/api/bson/objectid.rst
doc/api/bson/raw_bson.rst
doc/api/bson/regex.rst
doc/api/bson/son.rst
doc/api/bson/timestamp.rst
doc/api/bson/tz_util.rst
doc/api/gridfs/errors.rst
doc/api/gridfs/grid_file.rst
doc/api/gridfs/index.rst
doc/api/pymongo/bulk.rst
doc/api/pymongo/change_stream.rst
doc/api/pymongo/client_session.rst
doc/api/pymongo/collation.rst
doc/api/pymongo/collection.rst
doc/api/pymongo/command_cursor.rst
doc/api/pymongo/cursor.rst
doc/api/pymongo/cursor_manager.rst
doc/api/pymongo/database.rst
doc/api/pymongo/driver_info.rst
doc/api/pymongo/encryption.rst
doc/api/pymongo/encryption_options.rst
doc/api/pymongo/errors.rst
doc/api/pymongo/event_loggers.rst
doc/api/pymongo/index.rst
doc/api/pymongo/ismaster.rst
doc/api/pymongo/message.rst
doc/api/pymongo/mongo_client.rst
doc/api/pymongo/mongo_replica_set_client.rst
doc/api/pymongo/monitoring.rst
doc/api/pymongo/operations.rst
doc/api/pymongo/pool.rst
doc/api/pymongo/read_concern.rst
doc/api/pymongo/read_preferences.rst
doc/api/pymongo/results.rst
doc/api/pymongo/server_description.rst
doc/api/pymongo/son_manipulator.rst
doc/api/pymongo/topology_description.rst
doc/api/pymongo/uri_parser.rst
doc/api/pymongo/write_concern.rst
doc/developer/index.rst
doc/developer/periodic_executor.rst
doc/examples/aggregation.rst
doc/examples/authentication.rst
doc/examples/bulk.rst
doc/examples/collations.rst
doc/examples/copydb.rst
doc/examples/custom_type.rst
doc/examples/datetimes.rst
doc/examples/encryption.rst
doc/examples/geo.rst
doc/examples/gevent.rst
doc/examples/gridfs.rst
doc/examples/high_availability.rst
doc/examples/index.rst
doc/examples/mod_wsgi.rst
doc/examples/server_selection.rst
doc/examples/tailable.rst
doc/examples/tls.rst
doc/examples/uuid.rst
doc/pydoctheme/theme.conf
doc/pydoctheme/static/pydoctheme.css
doc/static/periodic-executor-refs.png
doc/static/sidebar.js
gridfs/__init__.py
gridfs/errors.py
gridfs/grid_file.py
pymongo/__init__.py
pymongo/_cmessagemodule.c
pymongo/aggregation.py
pymongo/auth.py
pymongo/auth_aws.py
pymongo/bulk.py
pymongo/change_stream.py
pymongo/client_options.py
pymongo/client_session.py
pymongo/collation.py
pymongo/collection.py
pymongo/command_cursor.py
pymongo/common.py
pymongo/compression_support.py
pymongo/cursor.py
pymongo/cursor_manager.py
pymongo/daemon.py
pymongo/database.py
pymongo/driver_info.py
pymongo/encryption.py
pymongo/encryption_options.py
pymongo/errors.py
pymongo/event_loggers.py
pymongo/helpers.py
pymongo/ismaster.py
pymongo/max_staleness_selectors.py
pymongo/message.py
pymongo/mongo_client.py
pymongo/mongo_replica_set_client.py
pymongo/monitor.py
pymongo/monitoring.py
pymongo/monotonic.py
pymongo/network.py
pymongo/ocsp_cache.py
pymongo/ocsp_support.py
pymongo/operations.py
pymongo/periodic_executor.py
pymongo/pool.py
pymongo/pyopenssl_context.py
pymongo/read_concern.py
pymongo/read_preferences.py
pymongo/response.py
pymongo/results.py
pymongo/saslprep.py
pymongo/server.py
pymongo/server_description.py
pymongo/server_selectors.py
pymongo/server_type.py
pymongo/settings.py
pymongo/socket_checker.py
pymongo/son_manipulator.py
pymongo/srv_resolver.py
pymongo/ssl_context.py
pymongo/ssl_match_hostname.py
pymongo/ssl_support.py
pymongo/thread_util.py
pymongo/topology.py
pymongo/topology_description.py
pymongo/uri_parser.py
pymongo/write_concern.py
pymongo.egg-info/PKG-INFO
pymongo.egg-info/SOURCES.txt
pymongo.egg-info/dependency_links.txt
pymongo.egg-info/requires.txt
pymongo.egg-info/top_level.txt
test/__init__.py
test/barrier.py
test/pymongo_mocks.py
test/qcheck.py
test/test_auth.py
test/test_auth_spec.py
test/test_binary.py
test/test_bson.py
test/test_bson_corpus.py
test/test_bulk.py
test/test_change_stream.py
test/test_client.py
test/test_client_context.py
test/test_cmap.py
test/test_code.py
test/test_collation.py
test/test_collection.py
test/test_command_monitoring_spec.py
test/test_common.py
test/test_connections_survive_primary_stepdown_spec.py
test/test_crud_v1.py
test/test_crud_v2.py
test/test_cursor.py
test/test_cursor_manager.py
test/test_custom_types.py
test/test_database.py
test/test_dbref.py
test/test_decimal128.py
test/test_discovery_and_monitoring.py
test/test_dns.py
test/test_encryption.py
test/test_errors.py
test/test_examples.py
test/test_grid_file.py
test/test_gridfs.py
test/test_gridfs_bucket.py
test/test_gridfs_spec.py
test/test_heartbeat_monitoring.py
test/test_json_util.py
test/test_legacy_api.py
test/test_max_staleness.py
test/test_mongos_load_balancing.py
test/test_monitor.py
test/test_monitoring.py
test/test_monotonic.py
test/test_objectid.py
test/test_ocsp_cache.py
test/test_pooling.py
test/test_pymongo.py
test/test_raw_bson.py
test/test_read_concern.py
test/test_read_preferences.py
test/test_read_write_concern_spec.py
test/test_replica_set_client.py
test/test_replica_set_reconfig.py
test/test_retryable_reads.py
test/test_retryable_writes.py
test/test_saslprep.py
test/test_sdam_monitoring_spec.py
test/test_server.py
test/test_server_description.py
test/test_server_selection.py
test/test_server_selection_rtt.py
test/test_session.py
test/test_son.py
test/test_son_manipulator.py
test/test_srv_polling.py
test/test_ssl.py
test/test_streaming_protocol.py
test/test_threads.py
test/test_timestamp.py
test/test_topology.py
test/test_transactions.py
test/test_uri_parser.py
test/test_uri_spec.py
test/test_write_concern.py
test/utils.py
test/utils_selection_tests.py
test/utils_spec_runner.py
test/version.py
test/atlas/test_connection.py
test/auth_aws/test_auth_aws.py
test/certificates/ca.pem
test/certificates/client.pem
test/certificates/crl.pem
test/certificates/password_protected.pem
test/certificates/server.pem
test/certificates/trusted-ca.pem
test/mod_wsgi_test/test_client.py
test/ocsp/test_ocsp.py
test/performance/perf_test.py
test/unicode/test_utf8.py
test/uri_options/ca.pem
test/uri_options/cert.pem
test/uri_options/client.pem
tools/README.rst
tools/benchmark.py
tools/clean.py
tools/fail_if_no_c.py
tools/ocsptest.py
/* CSS thuần cho Category, chuẩn BEM */
.category {
  max-width: 700px;
  margin: 40px auto;
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 2px 12px rgba(0,0,0,0.07);
  padding: 32px 24px;
}
.category__title {
  text-align: center;
  font-size: 2rem;
  font-weight: 600;
  margin-bottom: 32px;
  color: #1a237e;
}
.category__toolbar {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 24px;
  gap: 12px;
}
.category__add-btn {
  padding: 8px 24px;
  background: #1976d2;
  color: #fff;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
}
.category__add-btn:hover {
  background: #1565c0;
}
.category__table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 18px;
}
.category__table th,
.category__table td {
  border: 1px solid #e0e0e0;
  padding: 10px 8px;
  text-align: left;
}
.category__table th {
  background: #f5f5f5;
  font-weight: 600;
}
.category__action-btn {
  padding: 6px 18px;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  margin-right: 8px;
  background: #43a047;
  color: #fff;
  transition: background 0.2s;
}
.category__action-btn:last-child {
  margin-right: 0;
}
.category__action-btn--edit {
  background: #1976d2;
}
.category__action-btn--edit:hover {
  background: #1565c0;
}
.category__action-btn--delete {
  background: #e53935;
}
.category__action-btn--delete:hover {
  background: #b71c1c;
}

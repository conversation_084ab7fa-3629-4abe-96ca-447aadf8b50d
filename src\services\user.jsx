import { api } from "./instance";

export const loginService = async (data) => {
  try {
    const response = await api.post("login/", data);
    if (response.status === 200) {
      localStorage.setItem("user", JSON.stringify(response.data.user));
      localStorage.setItem("token", response.data.access);
    }
    return response;
  } catch (err) {
    return err.response || { status: 500, message: "Lỗi máy chủ" };
  }
};
export const registerService = async (data) => {
  try {
    const response = await api.post("register/", data);
    return response;
  } catch (err) {
    return err.response || { status: 500, message: "Lỗi máy chủ" };
  }
};

export const changePassword = async (data) => {
  const response = await api.put("changePassword/", data);
  return response;
};

export const updateProfile = async (data) => {
  const response = await api.put("changeInfo/", data);
  return response;
};
export const myProfile = async () => {
  const response = await api.get("profile/");
  return response.data;
};

export const logoutService = () => {
  localStorage.removeItem("user");
  localStorage.removeItem("token");
  window.location.href = "/";
};

export const getDoctor = async () => {
  try {
    const response = await api.get("doctor/");
    return response.data;
  } catch (error) {
    console.error("Lỗi khi lấy danh sách bác sĩ:", error);
    return [];
  }
};

export const getPatient = async () => { 
  try {
    const response = await api.get("patient/");
    return response.data;
  } catch (error) {
    console.error("Lỗi khi lấy danh sách bệnh nhân:", error);
    return [];
  }
}
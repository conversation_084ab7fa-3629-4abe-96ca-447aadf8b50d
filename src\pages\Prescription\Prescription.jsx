import React, { useEffect, useState } from "react";
import {
  getPrescriptionByStatus,
  updatePrescription,
} from "../../services/prescription";

const STATUS_CHOICES = [
  { value: "PENDING", label: "Chờ xử lý" },
  { value: "AWAITING_PICKUP", label: "Chờ nhận" },
  { value: "COMPLETED", label: "Đã nhận" },
];

export default function PrescriptionList() {
  const user = JSON.parse(localStorage.getItem("user"));
  const [status, setStatus] = useState(
    user && user.role === "PATIENT" ? "AWAITING_PICKUP" : "PENDING"
  );
  const [prescriptions, setPrescriptions] = useState([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    fetchPrescriptions();
  }, [status]);

  const fetchPrescriptions = async () => {
    setLoading(true);
    try {
      const response = await getPrescriptionByStatus(status);
      setPrescriptions(response);
    } catch (err) {
      console.error("Lỗi khi tải đơn thuốc:", err);
    } finally {
      setLoading(false);
    }
  };

  const updateStatus = async (id, newStatus) => {
    try {
      const response = await updatePrescription(id, { status: newStatus });
      if (response.status === 200) {
        fetchPrescriptions();
        alert("Cập nhật trạng thái thành công!");
      } else {
        alert("Cập nhật trạng thái thất bại!");
      }
    } catch (err) {
      console.error("Lỗi khi cập nhật trạng thái:", err);
      alert("Cập nhật thất bại");
    }
  };

  const Card = ({ prescription }) => {
    return (
      <div className="card shadow-sm mb-4" key={prescription.id}>
        <div className="card-header d-flex justify-content-between align-items-center bg-light">
          <strong>Đơn #{prescription.id}</strong>
          <div className="dropdown">
            {user && user.role === "PHARMACIST" ? (
              <>
                <button
                  className="btn btn-sm btn-outline-secondary dropdown-toggle"
                  type="button"
                  id={`dropdown-${prescription.id}`}
                  data-bs-toggle="dropdown"
                  aria-expanded="false"
                >
                  Trạng thái:{" "}
                  {STATUS_CHOICES.find((x) => x.value === status)?.label}
                </button>
                <ul
                  className="dropdown-menu"
                  aria-labelledby={`dropdown-${prescription.id}`}
                >
                  {STATUS_CHOICES.map(({ value, label }) => (
                    <li key={value}>
                      <button
                        className={`dropdown-item${
                          prescription.status === value ? " active" : ""
                        }`}
                        onClick={() => updateStatus(prescription.id, value)}
                      >
                        {label}
                      </button>
                    </li>
                  ))}
                </ul>
              </>
            ) : (
              <div style={{ fontSize: "14px", color: "gray" }}>
                Trạng thái:{" "}
                {STATUS_CHOICES.find((x) => x.value === status)?.label}
              </div>
            )}
          </div>
        </div>

        <div className="card-body">
          <p>
            <strong>Bệnh nhân:</strong>{" "}
            {prescription?.patient_detail?.first_name}{" "}
            {prescription?.patient_detail?.last_name}
          </p>
          <p>
            <strong>Bác sĩ:</strong> {prescription?.doctor_detail?.first_name}{" "}
            {prescription?.doctor_detail?.last_name}
          </p>
          <p>
            <strong>Ghi chú:</strong> {prescription.notes || <em>Không có</em>}
          </p>

          <h6 className="mt-4">Danh sách thuốc</h6>
          <div className="table-responsive">
            <table className="table table-striped table-sm">
              <thead>
                <tr>
                  <th>Tên thuốc</th>
                  <th>Số lượng</th>
                  <th>Tần suất</th>
                  <th>Giá</th>
                  <th>Hãng</th>
                  <th>HSD</th>
                </tr>
              </thead>
              <tbody>
                {prescription.items.map((item) => {
                  const med = item.medicine_detail || {};
                  return (
                    <tr key={item.id}>
                      <td>{med.name}</td>
                      <td>{item.quantity}</td>
                      <td>{item.frequency}</td>
                      <td>{med.price?.toLocaleString()}đ</td>
                      <td>{med.manufacturer}</td>
                      <td>{med.expiration_date}</td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="container my-5">
      <h2 className="mb-4">Danh sách đơn thuốc</h2>

      <div className="mb-4">
        <div className="btn-group gap-3" role="group">
          {STATUS_CHOICES.map(({ value, label }) =>
            user && user.role === "PATIENT" && value === "PENDING" ? null : (
              <button
                key={value}
                className={`btn btn-${
                  status === value ? "primary" : "outline-primary"
                } rounded-pill px-4`}
                onClick={() => setStatus(value)}
              >
                {label}
              </button>
            )
          )}
        </div>
      </div>

      {/* Danh sách đơn */}
      {loading ? (
        <div className="text-muted">Đang tải dữ liệu...</div>
      ) : prescriptions.length === 0 ? (
        <div className="alert alert-info">
          Không có đơn thuốc nào ở trạng thái{" "}
          {STATUS_CHOICES.find((x) => x.value === status)?.label}.
        </div>
      ) : (
        prescriptions.map((prescription) => (
          <Card key={prescription.id} prescription={prescription} />
        ))
      )}
    </div>
  );
}

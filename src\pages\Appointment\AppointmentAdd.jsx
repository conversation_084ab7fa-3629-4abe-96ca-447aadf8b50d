import React, { useEffect, useState } from "react";
import { getDoctor } from "../../services/user";
import {
  getAppointmentBooked,
  createAppointment,
  getShift,
  getDoctortAvailable,
} from "../../services/appointment";

const AppointmentScheduler = () => {
  const [doctors, setDoctors] = useState([]);
  const [doctorAvailable, setDoctorAvailable] = useState([]);
  const [selectedDoctor, setSelectedDoctor] = useState(null);
  const [appointments, setAppointments] = useState([]);
  const [shiftTime, setShiftTime] = useState([]);
  const [selectedWeek, setSelectedWeek] = useState(new Date());
  const [selectedAvailable, setSelectedAvailable] = useState(null);
  const [note, setNote] = useState("");
  const [showModal, setShowModal] = useState(false);

  const daysOfWeek = [
    "Thứ 2",
    "<PERSON><PERSON><PERSON> 3",
    "Thứ 4",
    "Thứ 5",
    "Th<PERSON> 6",
    "Th<PERSON> 7",
    "Chủ nhật",
  ];

  const user = JSON.parse(localStorage.getItem("user"));

  const getWeekDates = (date) => {
    const startOfWeek = new Date(date);
    const day = startOfWeek.getDay();
    const diff = day === 0 ? -6 : 1 - day;
    startOfWeek.setDate(startOfWeek.getDate() + diff);

    return [...Array(7)].map((_, i) => {
      const d = new Date(startOfWeek);
      d.setDate(d.getDate() + i);
      return d;
    });
  };

  const fetchDoctors = async () => {
    const response = await getDoctor();
    setDoctors(response);
  };

  const fetchShift = async () => {
    try {
      const response = await getShift();
      setShiftTime(response);
      console.log("Shift times:", response);
    } catch (error) {
      console.error("Error fetching shift times:", error);
    }
  };

  const fetchDoctorAvailable = async (doctor_id) => {
    try {
      const response = await getDoctortAvailable(doctor_id);
      setDoctorAvailable(response);
      console.log("Doctor availability:", response);
    } catch (error) {
      console.error("Error fetching doctor availability:", error);
    }
  };

  useEffect(() => {
    fetchShift();
    fetchDoctors();
  }, []);

  const handleDoctorChange = (e) => {
    fetchDoctorAvailable(e.target.value);
    setSelectedDoctor(e.target.value);
  };

  const fetchAppointments = async () => {
    const weekDates = getWeekDates(selectedWeek);
    const startDate = weekDates[0].toISOString().split("T")[0];
    const endDate = weekDates[6].toISOString().split("T")[0];

    try {
      const booked = await getAppointmentBooked(
        selectedDoctor,
        startDate,
        endDate
      );
      setAppointments(booked);
    } catch (err) {
      console.error("Error fetching appointments:", err);
    }
  };

  useEffect(() => {
    if (!selectedDoctor) return;
    fetchAppointments();
  }, [selectedDoctor, selectedWeek]);

  const getMondayOfWeek = (weekString) => {
    const [yearStr, weekStr] = weekString.split("-W");
    const year = parseInt(yearStr);
    const week = parseInt(weekStr);
    if (isNaN(year) || isNaN(week)) return new Date();

    const firstDayOfYear = new Date(year, 0, 1);
    const dayOffset = firstDayOfYear.getDay();
    const daysToMonday = dayOffset <= 4 ? 1 - dayOffset : 8 - dayOffset;
    const firstMonday = new Date(year, 0, 1 + daysToMonday);
    firstMonday.setDate(firstMonday.getDate() + (week - 1) * 7);
    return firstMonday;
  };

  const handleWeekChange = (e) => {
    const monday = getMondayOfWeek(e.target.value);
    setSelectedWeek(monday);
  };

  const formatDateToWeekInput = (date) => {
    const d = new Date(date);
    d.setDate(d.getDate() + 4 - (d.getDay() || 7));
    const yearStart = new Date(d.getFullYear(), 0, 1);
    const weekNo = Math.ceil(((d - yearStart) / 86400000 + 1) / 7);
    return `${d.getFullYear()}-W${weekNo.toString().padStart(2, "0")}`;
  };

  const isBooked = (available) => {
    for (const a of appointments) {
      if (
        a.doctor_availability.id === available.id &&
        a.status !== "CANCELLED" &&
        a.status !== "REJECTED"
      ) {
        return a.patient_id === user?.id ? "You" : "Booked";
      }
    }

    return "None";
  };

  const openBookingModal = (doctorAvailable) => {
    setSelectedAvailable(doctorAvailable);
    setNote("");
    setShowModal(true);
  };

  const confirmBooking = async () => {
    if (!selectedAvailable || !user) return;

    const data = {
      doctor_availability_id: selectedAvailable.id,
      notes: note,
    };

    try {
      const response = await createAppointment(data);
      if (response.status === 201) {
        fetchAppointments();
        alert("Đặt lịch thành công!");
        setShowModal(false);
        setSelectedAvailable(null);
        setNote("");
      } else {
        alert(response.data.error || "Lỗi khi đặt lịch!");
      }
    } catch (err) {
      alert("Có lỗi xảy ra khi đặt lịch!");
      console.error(err);
    }
  };

  const weekDates = getWeekDates(selectedWeek);
  const startOfWeek = weekDates[0].toLocaleDateString();
  const endOfWeek = weekDates[6].toLocaleDateString();

  return (
    <div className="container py-5">
      <h2 className="text-center mb-4 text-primary">Đặt lịch khám</h2>

      <div className="row mb-3">
        <div className="col-md-6">
          <label className="form-label fw-bold">Chọn bác sĩ:</label>
          <select
            className="form-select"
            onChange={handleDoctorChange}
            defaultValue=""
          >
            <option value="">-- Chọn bác sĩ --</option>
            {doctors.map((doc) => (
              <option key={doc.id} value={doc.id}>
                {doc.first_name} {doc.last_name}
              </option>
            ))}
          </select>
        </div>

        <div className="col-md-6">
          <label className="form-label fw-bold">Chọn tuần:</label>
          <input
            type="week"
            className="form-control"
            value={formatDateToWeekInput(selectedWeek)}
            onChange={handleWeekChange}
          />
          <div className="form-text">
            Tuần từ <strong>{startOfWeek}</strong> đến{" "}
            <strong>{endOfWeek}</strong>
          </div>
        </div>
      </div>

      {selectedDoctor ? (
        <div className="table-responsive mt-4">
          <table className="table table-bordered text-center align-middle table-hover">
            <thead className="table-primary">
              <tr>
                <th>Giờ</th>
                {weekDates.map((date, idx) => (
                  <th key={idx}>
                    {daysOfWeek[date.getDay() === 0 ? 6 : date.getDay() - 1]}
                    <br />
                    <span className="text-muted">
                      {date.toLocaleDateString()}
                    </span>
                  </th>
                ))}
              </tr>
            </thead>
            <tbody>
              {shiftTime.map((shift, idx) => (
                <tr key={idx}>
                  <td className="fw-bold">
                    {shift.start_time.substring(0, 5)} -{" "}
                    {shift.end_time.substring(0, 5)}
                  </td>
                  {weekDates.map((date, i) => {
                    const available = doctorAvailable.find(
                      (slot) =>
                        new Date(slot.date).toLocaleDateString() ===
                          date.toLocaleDateString() && slot.shift.id == shift.id
                    );
                    if (!available) {
                      return <td key={i} className="text-secondary"></td>;
                    }

                    const status = isBooked(available);

                    const slotDateTime = new Date(date);
                    const [hours, minutes, seconds] = shift.start_time
                      .split(":")
                      .map(Number);
                    slotDateTime.setHours(hours, minutes, 0, 0);

                    const now = new Date();

                    let btnClass = "btn-outline-success";
                    let text = "Đặt";
                    let disabled = false;

                    if (slotDateTime < now) {
                      btnClass = "btn-outline-secondary";
                      text = "Đã qua";
                      disabled = true;
                    } else if (status === "You") {
                      btnClass = "btn-danger";
                      text = "Bạn đã đặt";
                      disabled = true;
                    } else if (status === "Booked") {
                      btnClass = "btn-secondary";
                      text = "Không khả dụng";
                      disabled = true;
                    }

                    return (
                      <td key={i}>
                        <button
                          className={`btn btn-sm ${btnClass}`}
                          disabled={disabled}
                          onClick={() => openBookingModal(available)}
                        >
                          {text}
                        </button>
                      </td>
                    );
                  })}
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      ) : (
        <div className="alert alert-info mt-4 text-center">
          Vui lòng chọn bác sĩ để xem lịch khám.
        </div>
      )}

      {showModal && (
        <div
          className="modal fade show d-block"
          tabIndex="-1"
          style={{ backgroundColor: "rgba(0,0,0,0.5)" }}
        >
          <div className="modal-dialog">
            <div className="modal-content">
              <div className="modal-header">
                <h5 className="modal-title">Xác nhận đặt lịch</h5>
                <button
                  type="button"
                  className="btn-close"
                  onClick={() => setShowModal(false)}
                ></button>
              </div>
              <div className="modal-body">
                <p>
                  <strong>Ngày: </strong>
                  {new Date(selectedAvailable?.date).toLocaleDateString()}
                </p>
                <p>
                  <strong>Thời gian: </strong>
                  {selectedAvailable?.shift.start_time.substring(0, 5)} -{" "}
                  {selectedAvailable?.shift.end_time.substring(0, 5)}
                </p>
                <div className="mb-3">
                  <label htmlFor="note" className="form-label">
                    Ghi chú:
                  </label>
                  <input
                    type="text"
                    className="form-control"
                    id="note"
                    value={note}
                    onChange={(e) => setNote(e.target.value)}
                    placeholder="Nhập ghi chú (nếu có)..."
                  />
                </div>
              </div>
              <div className="modal-footer">
                <button
                  className="btn btn-secondary"
                  onClick={() => setShowModal(false)}
                >
                  Hủy
                </button>
                <button className="btn btn-primary" onClick={confirmBooking}>
                  Xác nhận
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AppointmentScheduler;

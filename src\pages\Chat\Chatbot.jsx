import React, { useState, useEffect, useRef } from "react";
import { postChat } from "../../services/chat";

function Chatbot() {
  const [messages, setMessages] = useState([
    {
      from: "bot",
      text: "Vui lòng mô tả triệu chứng của bạn, để tôi có thể chuẩn đoán.",
      time: new Date(),
    },
  ]);
  const [input, setInput] = useState("");
  const messagesEndRef = useRef(null);

  const sendMessage = async () => {
    if (!input.trim()) return;

    const userMessage = { from: "user", text: input, time: new Date() };
    setMessages((prev) => [...prev, userMessage]);

    const res = await postChat({ question: input });
    const botReply = {
      from: "bot",
      text: `Bạn có triệu chứng của bệnh: ${res.prediction}. <PERSON><PERSON><PERSON> có thể sử dụng: ${res.medicine}`,
      time: new Date(),
    };

    setMessages((prev) => [...prev, botReply]);
    setInput("");
  };

  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages]);

  const formatTime = (date) => {
    return date.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" });
  };

  return (
    <div className="container my-5" style={{ maxWidth: "600px" }}>
      <div className="card shadow-lg">
        <div
          className="card-header text-white text-center fw-bold"
          style={{ background: "#FFB22C" }}
        >
          Chatbot
        </div>
        <div
          className="card-body bg-light"
          style={{ height: "450px", overflowY: "auto" }}
        >
          {messages.map((msg, idx) => (
            <div
              key={idx}
              className={`d-flex flex-column ${
                msg.from === "user" ? "align-items-end" : "align-items-start"
              } mb-3`}
            >
              <div
                className="px-3 py-2 rounded-4 shadow-sm"
                style={{
                  backgroundColor: msg.from === "user" ? "#FFB22C" : "#fff",
                  color: msg.from === "user" ? "#fff" : "#000",
                  maxWidth: "75%",
                }}
              >
                {msg.text}
              </div>
              <small className="text-muted mt-1">
                {formatTime(new Date(msg.time))}
              </small>
            </div>
          ))}
          <div ref={messagesEndRef} />
        </div>
        <div className="card-footer d-flex">
          <input
            type="text"
            className="form-control me-2"
            value={input}
            onChange={(e) => setInput(e.target.value)}
            onKeyDown={(e) => e.key === "Enter" && sendMessage()}
            placeholder="Nhập tin nhắn..."
          />
          <button
            className="btn"
            onClick={sendMessage}
            style={{ background: "#FFB22C", color: "#fff" }}
          >
            Gửi
          </button>
        </div>
      </div>
    </div>
  );
}

export default Chatbot;

pycrfsuite/__init__.py,sha256=XsvkqhPXY9iHP8WBsyalpfJmWFzMrj_RrVJFQQNxoOo,28
pycrfsuite/__pycache__/__init__.cpython-310.pyc,,
pycrfsuite/__pycache__/_dumpparser.cpython-310.pyc,,
pycrfsuite/__pycache__/_logparser.cpython-310.pyc,,
pycrfsuite/_dumpparser.py,sha256=VEPCeoBU33VguujsAfF2sIcnFfsqwclVRxcbOLFeMUY,2714
pycrfsuite/_logparser.py,sha256=TzO4OnwNw5HLdFRrNX9uj6dTHsF4xiDJ8euxRg-_5AA,6121
pycrfsuite/_pycrfsuite.cp310-win_amd64.pyd,sha256=wNNP0x-DXH17m4wczsZZ7jELatH7BdMnybFl7VKOKgM,341504
pycrfsuite/_pycrfsuite.cpp,sha256=iAQ0vGBwldqUOgbDTcgNkvp7RDpqYcJbGCSfZ5G5nC8,1122285
pycrfsuite/_pycrfsuite.pyx,sha256=bRQsMSJDv2kvg4Nxe8GjYOER6tO1qSSK3IMbDXxqUig,24153
pycrfsuite/crfsuite_api.pxd,sha256=WHbJRPyoFH8zkxGUhlJ4zTP5uAIxwGGyQ3TDlRNVoCQ,2221
pycrfsuite/tagger_wrapper.hpp,sha256=lJmgbb5joqgp1xbrTXemUkAdQ6I7C4CYObCzg32YQDY,762
pycrfsuite/trainer_wrapper.cpp,sha256=V3zClFH0PkJuohQ8SyvieiamiECTBD_k7MtzFvz-Klc,1035
pycrfsuite/trainer_wrapper.hpp,sha256=0JSBENvzHC6rnBNv2w7XCuj52_DHXm4Jh4mbLvpVAm4,630
python_crfsuite-0.9.11.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
python_crfsuite-0.9.11.dist-info/LICENSE.txt,sha256=2cmx5zhWR7NF7Pv3lMWBeIRsmE_V9D9qdLue4KBzoF0,1117
python_crfsuite-0.9.11.dist-info/METADATA,sha256=ffAmL7-DUZ3ykBygrtSU4hN0kUBzLdrUPSaLOJ-MH6w,4395
python_crfsuite-0.9.11.dist-info/RECORD,,
python_crfsuite-0.9.11.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
python_crfsuite-0.9.11.dist-info/WHEEL,sha256=0ZjvOlAkRhiFz0IEm5kQrC9Db9zGCLzyOcgLl0kpzxU,101
python_crfsuite-0.9.11.dist-info/top_level.txt,sha256=zWZ9NH9khSnBsKz-xg1FH2x6luHD2J5x0PBHyICPcFM,11

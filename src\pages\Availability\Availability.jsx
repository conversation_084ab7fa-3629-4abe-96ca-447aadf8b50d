import React, { useEffect, useState } from "react";
import {
  getAppointmentBooked,
  getShift,
  getDoctortAvailable,
  updateAppointment,
} from "../../services/appointment";

const AppointmentScheduler = () => {
  const [doctorAvailable, setDoctorAvailable] = useState([]);
  const [appointments, setAppointments] = useState([]);
  const [selectedAppointment, setSelectedAppointment] = useState(null);
  const [shiftTime, setShiftTime] = useState([]);
  const [selectedWeek, setSelectedWeek] = useState(new Date());
  const [showModal, setShowModal] = useState(false);

  const daysOfWeek = [
    "Thứ 2",
    "Thứ 3",
    "Thứ 4",
    "Thứ 5",
    "Thứ 6",
    "Thứ 7",
    "Chủ nhật",
  ];

  const user = JSON.parse(localStorage.getItem("user"));

  const getWeekDates = (date) => {
    const startOfWeek = new Date(date);
    const day = startOfWeek.getDay();
    const diff = day === 0 ? -6 : 1 - day;
    startOfWeek.setDate(startOfWeek.getDate() + diff);

    return [...Array(7)].map((_, i) => {
      const d = new Date(startOfWeek);
      d.setDate(d.getDate() + i);
      return d;
    });
  };

  const fetchShift = async () => {
    try {
      const response = await getShift();
      setShiftTime(response);
      console.log("Shift times:", response);
    } catch (error) {
      console.error("Error fetching shift times:", error);
    }
  };

  const fetchDoctorAvailable = async () => {
    try {
      const response = await getDoctortAvailable(user?.id);
      setDoctorAvailable(response);
      console.log("Doctor availability:", response);
    } catch (error) {
      console.error("Error fetching doctor availability:", error);
    }
  };

  useEffect(() => {
    fetchShift();
  }, []);

  const fetchAppointments = async () => {
    const weekDates = getWeekDates(selectedWeek);
    const startDate = weekDates[0].toISOString().split("T")[0];
    const endDate = weekDates[6].toISOString().split("T")[0];

    try {
      const booked = await getAppointmentBooked(
        user?.id,
        startDate,
        endDate,
        true
      );
      setAppointments(booked);
      console.log("Booked appointments:", booked);
    } catch (err) {
      console.error("Error fetching appointments:", err);
    }
  };

  useEffect(() => {
    fetchDoctorAvailable();
    fetchAppointments();
  }, [selectedWeek]);

  const getMondayOfWeek = (weekString) => {
    const [yearStr, weekStr] = weekString.split("-W");
    const year = parseInt(yearStr);
    const week = parseInt(weekStr);
    if (isNaN(year) || isNaN(week)) return new Date();

    const firstDayOfYear = new Date(year, 0, 1);
    const dayOffset = firstDayOfYear.getDay();
    const daysToMonday = dayOffset <= 4 ? 1 - dayOffset : 8 - dayOffset;
    const firstMonday = new Date(year, 0, 1 + daysToMonday);
    firstMonday.setDate(firstMonday.getDate() + (week - 1) * 7);
    return firstMonday;
  };

  const handleWeekChange = (e) => {
    const monday = getMondayOfWeek(e.target.value);
    setSelectedWeek(monday);
  };

  const formatDateToWeekInput = (date) => {
    const d = new Date(date);
    d.setDate(d.getDate() + 4 - (d.getDay() || 7));
    const yearStart = new Date(d.getFullYear(), 0, 1);
    const weekNo = Math.ceil(((d - yearStart) / 86400000 + 1) / 7);
    return `${d.getFullYear()}-W${weekNo.toString().padStart(2, "0")}`;
  };

  const openBookingModal = (appointment) => {
    setSelectedAppointment(appointment);
    setShowModal(true);
  };

  const updateBooking = async (status) => {
    if (!selectedAppointment || !user) return;
    try {
      const response = await updateAppointment(selectedAppointment.id, status);
      if (response.status === 200) {
        fetchAppointments();
        alert("Cập nhật lịch hẹn thành công!");
      } else {
        alert(response.data.error || "Có lỗi đã xảy ra!");
      }
    } catch (err) {
      alert("Có lỗi xảy ra khi cập nhật lịch hẹn.");
      console.error(err);
    }
    setShowModal(false);
    setSelectedAppointment(null);
  };

  const weekDates = getWeekDates(selectedWeek);
  const startOfWeek = weekDates[0].toLocaleDateString();
  const endOfWeek = weekDates[6].toLocaleDateString();

  return (
    <div className="container py-5">
      <h2 className="text-center mb-4 text-primary">Quản lí lịch khám</h2>

      <div>
        <label className="form-label fw-bold">Chọn tuần:</label>
        <input
          type="week"
          className="form-control"
          value={formatDateToWeekInput(selectedWeek)}
          onChange={handleWeekChange}
        />
        <div className="form-text">
          Tuần từ <strong>{startOfWeek}</strong> đến{" "}
          <strong>{endOfWeek}</strong>
        </div>
      </div>

      <div className="table-responsive mt-4">
        <table className="table table-bordered text-center align-middle table-hover">
          <thead className="table-primary">
            <tr>
              <th>Giờ</th>
              {weekDates.map((date, idx) => (
                <th key={idx}>
                  {daysOfWeek[date.getDay() === 0 ? 6 : date.getDay() - 1]}
                  <br />
                  <span className="text-muted">
                    {date.toLocaleDateString()}
                  </span>
                </th>
              ))}
            </tr>
          </thead>
          <tbody>
            {shiftTime.map((shift, idx) => (
              <tr key={idx}>
                <td className="fw-bold">
                  {shift.start_time.slice(0, 5)} - {shift.end_time.slice(0, 5)}
                </td>

                {weekDates.map((date, i) => {
                  const dateStr = date.toLocaleDateString();

                  const available = doctorAvailable.find(
                    (slot) =>
                      new Date(slot.date).toLocaleDateString() === dateStr &&
                      slot.shift.id === shift.id
                  );

                  if (!available) {
                    return <td key={i} className="text-secondary"></td>;
                  }

                  const appointment = appointments.find(
                    (app) =>
                      app.doctor_availability.id === available.id &&
                      app.status !== "CANCELLED" &&
                      app.status !== "REJECTED"
                  );

                  const slotDateTime = new Date(date);
                  const [hours, minutes] = shift.start_time
                    .split(":")
                    .map(Number);
                  slotDateTime.setHours(hours, minutes, 0, 0);

                  const now = new Date();

                  let btnClass = "btn-outline-secondary";
                  let text = "Đã qua";
                  let disabled = true;

                  if (slotDateTime >= now) {
                    if (!appointment) {
                      btnClass = "btn-outline-primary";
                      text = "Trống";
                    } else if (appointment.status === "PENDING") {
                      btnClass = "btn-warning";
                      text = "Chờ xác nhận";
                      disabled = false;
                    } else if (appointment.status === "CONFIRMED") {
                      btnClass = "btn-success";
                      text = "Đã xác nhận";
                    }
                  }

                  return (
                    <td key={i}>
                      <button
                        className={`btn btn-sm ${btnClass}`}
                        disabled={disabled}
                        onClick={() => openBookingModal(appointment)}
                      >
                        {text}
                      </button>
                    </td>
                  );
                })}
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {showModal && (
        <div
          className="modal fade show d-block"
          tabIndex="-1"
          style={{ backgroundColor: "rgba(0,0,0,0.5)" }}
        >
          <div className="modal-dialog">
            <div className="modal-content">
              <div className="modal-header">
                <h5 className="modal-title">Xác nhận đặt lịch</h5>
                <button
                  type="button"
                  className="btn-close"
                  onClick={() => setShowModal(false)}
                ></button>
              </div>
              <div className="modal-body">
                <p>
                  <strong>Khách hàng: </strong>
                  {selectedAppointment?.patient?.first_name || ""}{" "}
                  {selectedAppointment?.patient?.last_name || ""}
                </p>
                <p>
                  <strong>Ngày: </strong>
                  {new Date(
                    selectedAppointment?.doctor_availability.date
                  ).toLocaleDateString()}
                </p>
                <p>
                  <strong>Thời gian: </strong>
                  {selectedAppointment?.doctor_availability.shift.start_time.substring(
                    0,
                    5
                  )}{" "}
                  -{" "}
                  {selectedAppointment?.doctor_availability.shift.end_time.substring(
                    0,
                    5
                  )}
                </p>
                <p>
                  <strong>Ghi chú: </strong>
                  {selectedAppointment?.notes || "Không có ghi chú"}
                </p>
              </div>

              <div className="modal-footer justify-content-between">
                <button
                  className="btn btn-danger"
                  onClick={() => updateBooking("REJECTED")}
                >
                  Từ chối
                </button>
                <button
                  className="btn btn-primary"
                  onClick={() => updateBooking("CONFIRMED")}
                >
                  Đồng ý
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AppointmentScheduler;

import { api } from "./instance";

export const getCategory = async () => {
    try {
        const response = await api.get("/categories/");
        return response.data;
    } catch (error) {
        console.error("Lỗi khi lấy danh mục:", error);
        return [];
    }
}

export const getCategoryById = async (id) => {
    try {
        const response = await api.get(`/categories/${id}/`);
        return response.data;
    } catch (error) {
        console.error("Lỗi khi lấy thông tin danh mục:", error);
        return null;
    }
}

export const createCategory = async (category) => {
    try {
        const response = await api.post("/categories/", category);
        return response;
    } catch (error) {
        console.error("Lỗi khi tạo danh mục:", error);
        return null;
    }
}

export const updateCategory = async (id, category) => {
    try {
        const response = await api.put(`/categories/${id}/`, category);
        return response;
    } catch (error) {
        console.error("Lỗi khi cập nhật danh mục:", error);
        return null;
    }
}

export const deleteCategory = async (id) => {
    try {
        const response = await api.delete(`/categories/${id}/`);
        return response;
    } catch (error) {
        console.error("Lỗi khi xóa danh mục:", error);
        return null;
    }
}

/* CSS thuần cho Prescription, chuẩn BEM */
.prescription {
  max-width: 800px;
  margin: 40px auto;
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 2px 12px rgba(0,0,0,0.07);
  padding: 24px 16px;
}
.prescription__title {
  text-align: center;
  font-size: 2rem;
  font-weight: 600;
  margin-bottom: 32px;
  color: #1a237e;
}
.prescription__toolbar {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 24px;
  gap: 12px;
}
.prescription__filter-btn {
  padding: 6px 18px;
  border: 1px solid #bdbdbd;
  border-radius: 8px;
  background: #f5f5f5;
  color: #333;
  font-weight: 500;
  cursor: pointer;
  transition: background 0.2s, color 0.2s, border 0.2s;
}
.prescription__filter-btn.active {
  background: #1976d2;
  color: #fff;
  border-color: #1976d2;
}
.prescription__list {
  margin-top: 24px;
}
.prescription__card {
  background: #fafbfc;
  border: 1px solid #e0e0e0;
  border-radius: 12px;
  box-shadow: 0 1px 4px rgba(0,0,0,0.04);
  padding: 20px 18px;
  margin-bottom: 20px;
}
.prescription__card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}
.prescription__card-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #263238;
  margin: 0;
}
.prescription__badge {
  display: inline-block;
  min-width: 80px;
  text-align: center;
  padding: 6px 0;
  border-radius: 8px;
  font-size: 0.95rem;
  font-weight: 500;
  color: #fff;
}
.prescription__badge--pending {
  background: #ffb300;
  color: #333;
}
.prescription__badge--awaiting_pickup {
  background: #1976d2;
}
.prescription__badge--completed {
  background: #43a047;
}
.prescription__info {
  margin: 2px 0;
  font-size: 1rem;
  color: #374151;
}
.prescription__action-group {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  margin-top: 16px;
}
.prescription__action-btn {
  padding: 6px 22px;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: background 0.2s;
}
.prescription__action-btn--danger {
  background: #e53935;
  color: #fff;
}
.prescription__action-btn--danger:hover {
  background: #b71c1c;
}
.prescription__action-btn--success {
  background: #43a047;
  color: #fff;
}
.prescription__action-btn--success:hover {
  background: #2e7d32;
}

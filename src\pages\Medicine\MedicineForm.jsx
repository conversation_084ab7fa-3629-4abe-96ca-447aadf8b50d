// src/pages/MedicineForm.js
import React, { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { getCategory } from "../../services/category";
import {
  createMedicine,
  getMedicineById,
  updateMedicine,
} from "../../services/medicine";
import "./MedicineForm.css";

const MedicineForm = () => {
  const { id } = useParams(); // lấy id từ URL nếu đang sửa thuốc
  const navigate = useNavigate();
  const [formData, setFormData] = useState({
    name: "",
    category_id: "",
    description: "",
    price: "",
    stock: "",
    expiration_date: "",
    manufacturer: "",
  });
  const [categories, setCategories] = useState([]);
  const [message, setMessage] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  // Fetch danh mục thuốc
  const fetchCategories = async () => {
    try {
      const res = await getCategory();
      setCategories(res);
    } catch (err) {
      console.error(err);
      setMessage("Lỗi khi tải danh mục thuốc!");
    }
  };

  // Fetch thông tin thuốc khi đang sửa
  const fetchMedicine = async () => {
    try {
      const res = await getMedicineById(id);
      setFormData({ ...res, category_id: res.category?.id });
    } catch (err) {
      console.error(err);
      setMessage("Lỗi khi tải thông tin thuốc!");
    }
  };

  // Dùng useEffect để fetch dữ liệu ban đầu
  useEffect(() => {
    fetchCategories();
    if (id) {
      fetchMedicine();
    }
  }, [id]);

  // Handle sự kiện thay đổi giá trị trong form
  const handleChange = (e) => {
    setFormData((prev) => ({ ...prev, [e.target.name]: e.target.value }));
  };

  // Xử lý form khi submit
  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      let res;
      if (id) {
        res = await updateMedicine(id, formData); // Cập nhật thuốc
        if (res.status === 200) {
          setMessage("Cập nhật thuốc thành công!");
        } else {
          setMessage("Lỗi cập nhật thuốc!");
        }
      } else {
        res = await createMedicine(formData); // Thêm thuốc mới
        if (res.status === 201) {
          setMessage("Thêm thuốc thành công!");
        } else {
          setMessage("Lỗi thêm thuốc!");
        }
      }
      setIsLoading(false);
      navigate("/medicine");
    } catch (err) {
      setMessage("Lỗi gửi dữ liệu!");
      console.error(err);
      setIsLoading(false);
    }
  };

  // Xử lý form khi bấm hủy
  const handleCancel = () => {
    navigate("/medicine");
  };

  return (
    <div className="medicine-form">
      <h2 className="medicine-form__title">
        {id ? "Chỉnh sửa thuốc" : "Thêm thuốc mới"}
      </h2>

      {message && <div className="medicine-form__error">{message}</div>}

      <form onSubmit={handleSubmit} className="medicine-form__form">
        <div className="medicine-form__group">
          <label className="medicine-form__label">Tên thuốc</label>
          <input
            type="text"
            name="name"
            className="medicine-form__input"
            value={formData.name}
            onChange={handleChange}
            required
          />
        </div>

        <div className="medicine-form__group">
          <label className="medicine-form__label">Danh mục</label>
          <select
            className="medicine-form__input"
            name="category_id"
            value={formData.category_id}
            onChange={handleChange}
            required
          >
            <option value="">-- Chọn danh mục --</option>
            {categories.map((cat) => (
              <option key={cat.id} value={cat.id}>
                {cat.name}
              </option>
            ))}
          </select>
        </div>

        <div className="medicine-form__group">
          <label className="medicine-form__label">Mô tả</label>
          <textarea
            name="description"
            className="medicine-form__input"
            rows="2"
            value={formData.description}
            onChange={handleChange}
            required
          />
        </div>

        <div className="medicine-form__group">
          <label className="medicine-form__label">Giá (VNĐ)</label>
          <input
            type="number"
            name="price"
            className="medicine-form__input"
            value={formData.price}
            onChange={handleChange}
            required
          />
        </div>

        <div className="medicine-form__group">
          <label className="medicine-form__label">Tồn kho</label>
          <input
            type="number"
            name="stock"
            className="medicine-form__input"
            value={formData.stock}
            onChange={handleChange}
            required
          />
        </div>

        <div className="medicine-form__group">
          <label className="medicine-form__label">Ngày hết hạn</label>
          <input
            type="date"
            name="expiration_date"
            className="medicine-form__input"
            value={formData.expiration_date}
            onChange={handleChange}
            required
          />
        </div>

        <div className="medicine-form__group">
          <label className="medicine-form__label">Nhà sản xuất</label>
          <input
            type="text"
            name="manufacturer"
            className="medicine-form__input"
            value={formData.manufacturer}
            onChange={handleChange}
            required
          />
        </div>

        <div className="medicine-form__action-group">
          <button
            type="submit"
            className="medicine-form__button"
            disabled={isLoading}
          >
            {isLoading ? "Đang xử lý..." : id ? "Cập nhật" : "Thêm"}
          </button>
          <button
            type="button"
            className="medicine-form__button medicine-form__button--cancel"
            onClick={handleCancel}
          >
            Hủy
          </button>
        </div>
      </form>
    </div>
  );
};

export default MedicineForm;

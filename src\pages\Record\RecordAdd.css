/* CSS thuần cho <PERSON>, chu<PERSON><PERSON>EM */
.record-add {
  max-width: 500px;
  margin: 40px auto;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0,0,0,0.07);
  padding: 32px 24px;
}
.record-add__title {
  text-align: center;
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 24px;
  color: #1976d2;
}
.record-add__form {
  display: flex;
  flex-direction: column;
  gap: 16px;
}
.record-add__input {
  padding: 10px 12px;
  border: 1px solid #bdbdbd;
  border-radius: 8px;
  font-size: 1rem;
}
.record-add__button {
  padding: 10px 0;
  background: #1976d2;
  color: #fff;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  font-size: 1.1rem;
  cursor: pointer;
  margin-top: 8px;
}
.record-add__button:hover {
  background: #1565c0;
}
.record-add__error {
  color: #e53935;
  font-size: 0.95rem;
  margin-bottom: 8px;
  text-align: center;
}

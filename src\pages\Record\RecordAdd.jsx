import React, { useState } from "react";
import { createRecord } from "../../services/record";

export default function RecordForm() {
  const [formData, setFormData] = useState({
    patient_id: "",
    diagnosis: "",
    treatment: "",
  });

  const [isLoading, setIsLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState("");

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsLoading(true);
    setSuccess(false);
    setError("");

    try {
      const response = await createRecord(formData);
      if (response.status === 201) {
        setSuccess(true);
        setFormData({
          patient_id: "",
          diagnosis: "",
          treatment: "",
        });
      } else {
        setError(" Lỗi khi t<PERSON><PERSON> hồ sơ bệnh án");
      }
    } catch (err) {
      setError(" Lỗi khi tạo hồ sơ bệnh án");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="container my-5">
      <h3>Tạo Hồ sơ bệnh án</h3>
      <form className="mt-4" onSubmit={handleSubmit}>
        <div className="mb-3">
          <label className="form-label">ID bệnh nhân</label>
          <input
            type="number"
            className="form-control"
            name="patient_id"
            value={formData.patient_id}
            onChange={handleChange}
            required
          />
        </div>

        <div className="mb-3">
          <label className="form-label">Chẩn đoán</label>
          <input
            type="text"
            className="form-control"
            name="diagnosis"
            value={formData.diagnosis}
            onChange={handleChange}
            required
          />
        </div>

        <div className="mb-3">
          <label className="form-label">Phác đồ điều trị</label>
          <textarea
            className="form-control"
            name="treatment"
            rows={3}
            value={formData.treatment}
            onChange={handleChange}
            required
          ></textarea>
        </div>

        <button type="submit" className="btn btn-primary" disabled={isLoading}>
          {isLoading ? "Đang gửi..." : "Lưu hồ sơ"}
        </button>

        {success && (
          <div className="alert alert-success mt-3">
            ✅ Hồ sơ đã được tạo thành công!
          </div>
        )}
        {error && <div className="alert alert-danger mt-3">{error}</div>}
      </form>
    </div>
  );
}

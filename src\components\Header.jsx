import { <PERSON> } from "react-router-dom";
import "bootstrap/dist/css/bootstrap.min.css";
import "bootstrap/dist/js/bootstrap.bundle.min.js";
import { Container, Navbar, Nav, NavDropdown } from "react-bootstrap";
import { logoutService } from "../services/user.jsx";

const Header = ({ user }) => {
  return (
    <Navbar expand="lg" className="shadow">
      <Container>
        <Navbar.Brand as={Link} to="/" className="me-5">
          <h1>
            <span style={{color: "var(--bs-primary)"}}>Health </span>
            <span>Care</span>
          </h1>
        </Navbar.Brand>

        {/* Toggle button for mobile */}
        <Navbar.Toggle aria-controls="navbar-nav"/>

        <Navbar.Collapse id="navbar-nav">
          {/* Navigation Links */}
          <Nav className="me-auto">
            <Nav.Link as={Link} to="/"><PERSON><PERSON> chủ</Nav.Link>
            {user && user.role === "PATIENT" && (
              <>
                <Nav.Link as={Link} to="/appointment"><PERSON><PERSON><PERSON> kh<PERSON>m</Nav.Link>
                <Nav.Link as={Link} to="/prescription">Đơn thuốc</Nav.Link>
                <Nav.Link as={Link} to="/record">Hồ sơ bệnh án</Nav.Link>
                <Nav.Link as={Link} to="/chat">Chatbot</Nav.Link>
              </>
            )}

            {user && user.role === "DOCTOR" && (
              <>
                <Nav.Link as={Link} to="/appointment">Quản lí lịch khám</Nav.Link>
                <Nav.Link as={Link} to="/lab/Add">Đặt xét nghiệm</Nav.Link>
                <Nav.Link as={Link} to="/prescription/add">Kê đơn điều trị</Nav.Link>
                <Nav.Link as={Link} to="/prescription">Lịch sử kê đơn</Nav.Link>
              </>
            )}

            {user && user.role === "PHARMACIST" && (
              <>
                <Nav.Link as={Link} to="/medicine">Quản lí thuốc</Nav.Link>
                <Nav.Link as={Link} to="/category">Quản lí danh mục</Nav.Link>
                <Nav.Link as={Link} to="/prescription">Quản lí đơn thuốc</Nav.Link>
              </>
            )}

            {user && user.role === "LAB_TECH" && (
              <>
                <Nav.Link as={Link} to="/lab">Quản lí xét nghiệm</Nav.Link>
              </>
            )}
            
            
          </Nav>

          {/* User Authentication */}
          <Nav>
            {user? (
              <>
                <Nav.Link as={Link} to="/profile">Tài khoản</Nav.Link>
                <Nav.Link onClick={logoutService}>Đăng xuất</Nav.Link>
              </>
            ) : (
              <>
                <Nav.Link as={Link} to="/login">Đăng nhập</Nav.Link>
                <Nav.Link as={Link} to="/register">Đăng ký</Nav.Link>
              </>
            )}
          </Nav>
        </Navbar.Collapse>
      </Container>
    </Navbar>
  );
};

export default Header;

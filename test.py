import requests
import json
from datetime import datetime, timedelta
import random

f = open('medicine.json', 'r', encoding='utf-8')
data = json.load(f)
f.close()

users = [{
    "email": "<EMAIL>",
    "password": "123",
    "first_name": "<PERSON><PERSON><PERSON>",
    "last_name":"A",
    "role":"PATIENT",
},{
    "email": "<EMAIL>",
    "password": "123",
    "first_name": "<PERSON> Ng<PERSON>",
    "last_name":"B",
    "role":"DOCTOR",
},
{
    "email": "<EMAIL>",
    "password": "123",
    "first_name": "<PERSON><PERSON><PERSON>",
    "last_name":"C",
    "role":"PHARMACIST",
}]

shift = [
    {
        "start_time": "08:00:00",
        "end_time": "9:00:00"
    },
    {
        "start_time": "09:00:00",
        "end_time": "10:00:00"
    },
    {
        "start_time": "10:00:00",
        "end_time": "11:00:00"
    },
    {
        "start_time": "11:00:00",
        "end_time": "12:00:00"
    },
    {
        "start_time": "13:00:00",
        "end_time": "14:00:00"
    },
    {
        "start_time": "14:00:00",
        "end_time": "15:00:00"
    },
    {
        "start_time": "15:00:00",
        "end_time": "16:00:00"
    },
    {
        "start_time": "16:00:00",
        "end_time": "17:00:00"
    },
    {
        "start_time": "17:00:00",
        "end_time": "18:00:00"
    }
]

api_url = "http://127.0.0.1:8000"

def create_customer():
    for user in users:
        response = requests.post(f"{api_url}/register/", json=user)
        print(f"Registering {user['email']}: {response.status_code}")

def create_availability(doctor_id, token):
    start_date = "2025-05-20"

    for i in range(7):
        date = (datetime.strptime(start_date, "%Y-%m-%d") + timedelta(days=i)).strftime("%Y-%m-%d")
        
        num_shifts = random.randint(3, 5)
        random_shifts = random.sample(range(1, len(shift) + 1), num_shifts)
        for s in random_shifts:
            response = requests.post(f"{api_url}/doctorAvailability/", json={
                "doctor_id": doctor_id,
                "date": date,
                "shift_id": s
            }, headers={
                "Authorization": f"Bearer {token}"
            })
            print("Doctor availability creation response:", response.status_code)


create_customer()

response = requests.post(f"{api_url}/login/", json={
    "email": "<EMAIL>",
    "password": "123"
})
if response.status_code == 200:
    token = response.json().get("access")

    for cate in data["categories"]:
        response = requests.post(f"{api_url}/categories/", json=cate, headers={
            "Authorization": f"Bearer {token}"
        })
        print(response.status_code)

    for medi in data["medicines"]:
        response = requests.post(f"{api_url}/medicines/", json=medi, headers={
            "Authorization": f"Bearer {token}"
        })
        print(response.status_code)

    for shift_data in shift:
        response = requests.post(f"{api_url}/shifts/", json=shift_data, headers={
            "Authorization": f"Bearer {token}"
        })
        print("Shift creation response:", response.status_code)

    create_availability(2, token)
                          
else:
    print("Login failed:", response.status_code, response.text)


import { Pagination } from "react-bootstrap";

const PaginationComp = ({page, setPage, totalPages}) => {
    const getPaginationItems = () => {
        const items = [];
        const maxVisible = 10; // số lượng trang hiển thị tối đa

        let startPage = Math.max(1, page - Math.floor(maxVisible / 2));
        let endPage = startPage + maxVisible - 1;

        if (endPage > totalPages) {
            endPage = totalPages;
            startPage = Math.max(1, endPage - maxVisible + 1);
        }

        // Dấu ba chấm trước
        if (startPage > 1) {
            items.push(
                <Pagination.Ellipsis key="start-ellipsis" disabled />
            );
        }

        // Các trang hiển thị
        for (let i = startPage; i <= endPage; i++) {
            items.push(
                <Pagination.Item
                    key={i}
                    active={page === i}
                    onClick={() => setPage(i)}
                >
                    {i}
                </Pagination.Item>
            );
        }

        // Dấu ba chấm sau
        if (endPage < totalPages) {
            items.push(
                <Pagination.Ellipsis key="end-ellipsis" disabled />
            );
        }

        return items;
    };

    return (
        (totalPages > 1)?(
        <Pagination className="justify-content-center mt-5 align-items-center custom-pagination">
            <Pagination.Prev
                onClick={() => setPage((prev) => Math.max(prev - 1, 1))}
                disabled={page === 1}
            />
            {getPaginationItems()}
            <Pagination.Next
                onClick={() => setPage((prev) => Math.min(prev + 1, totalPages))}
                disabled={page === totalPages}
            />
        </Pagination>):""
    );
};

export default PaginationComp;

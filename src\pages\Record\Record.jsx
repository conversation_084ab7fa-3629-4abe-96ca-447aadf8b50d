import React, { useEffect, useState } from "react";
import { getRecord } from "../../services/record";

const RecordList = () => {
  const [records, setRecords] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchRecords = async () => {
      try {
        const response = await getRecord();
        setRecords(response);
      } catch (error) {
        console.error("Lỗi khi tải hồ sơ bệnh nhân:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchRecords();
  }, []);

  if (loading) {
    return <div className="text-center my-5">Đang tải dữ liệu...</div>;
  }

  return (
    <div className="container my-5">
      <h2 className="mb-4 text-center"><PERSON><PERSON> sách hồ sơ bệnh án</h2>
      {records.length === 0 ? (
        <div className="alert alert-info text-center"><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON> hồ sơ nào.</div>
      ) : (
        <div>
          {records.map((record) => (
            <div className="card h-100" key={record.id}>
              <div className="card-body">
                <h5 className="card-title text-primary mb-3">
                  Hồ sơ #{record.id}
                </h5>
                <p className="mx-0 my-2">
                  <strong>Chẩn đoán:</strong> {record.diagnosis}
                </p>
                <p className="mx-0 my-2">
                  <strong>Điều trị:</strong> {record.treatment}
                </p>
              </div>
              <div className="card-footer text-muted small">
                Ngày tạo: {new Date(record.created_at).toLocaleString("vi-VN")}
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default RecordList;

import React, { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { getLabByStatus } from "../../services/lab";

export default function LabList() {
  const [labs, setLabs] = useState([]);
  const [status, setStatus] = useState("PENDING");
  const navigate = useNavigate();

  const fetchLabs = async () => {
    try {
      const response = await getLabByStatus(status);
      setLabs(response);
    } catch (err) {
      console.error(err);
      alert(" Không thể tải danh sách xét nghiệm");
    }
  };

  useEffect(() => {
    fetchLabs();
  }, [status]);

  return (
    <div className="container my-5">
      <h3>Danh sách xét nghiệm</h3>

      {/* Nút lọc trạng thái */}
      <div className="mb-3">
        <button
          className={`btn me-2 ${
            status === "PENDING" ? "btn-primary" : "btn-outline-primary"
          }`}
          onClick={() => setStatus("PENDING")}
        >
          Chờ kết quả
        </button>
        <button
          className={`btn ${
            status === "COMPLETED" ? "btn-success" : "btn-outline-success"
          }`}
          onClick={() => setStatus("COMPLETED")}
        >
          Đã hoàn thành
        </button>
      </div>

      <table className="table table-striped mt-3">
        <thead className="table-light">
          <tr>
            <th>ID</th>
            <th>Bệnh nhân</th>
            <th>Loại</th>
            <th>Mô tả</th>
            <th>Kết quả</th>
            <th>Trạng thái</th>
            <th>Hành động</th>
          </tr>
        </thead>
        <tbody>
          {labs.map((lab) => (
            <tr key={lab.id}>
              <td>{lab.id}</td>
              <td>{lab.patient_id}</td>
              <td>{lab.type}</td>
              <td>{lab.description}</td>
              <td>{lab.result || "—"}</td>
              <td>
                <span
                  className={`badge bg-${
                    lab.status === "COMPLETED" ? "success" : "warning"
                  }`}
                >
                  {lab.status}
                </span>
              </td>
              <td>
                <button
                  className="btn btn-sm btn-outline-primary"
                  onClick={() => navigate(`/lab/result/${lab.id}`)}
                >
                  Cập nhật kết quả
                </button>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
}

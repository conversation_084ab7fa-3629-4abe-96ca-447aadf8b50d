// src/pages/MedicineManager.js
import React, { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { getMedicine, deleteMedicine } from "../../services/medicine";
import "./Medicine.css";

const MedicineManager = () => {
  const [medicines, setMedicines] = useState([]);
  const [message, setMessage] = useState("");
  const navigate = useNavigate();

  const fetchMedicines = async () => {
    try {
      const res = await getMedicine();
      setMedicines(res);
    } catch (err) {
      console.error(err);
    }
  };

  useEffect(() => {
    fetchMedicines();
  }, []);

  const handleDelete = async (id) => {
    if (!window.confirm("Bạn có chắc chắn muốn xoá thuốc này?")) return;
    try {
      const res = await deleteMedicine(id);
      if (res.status === 204) {
        setMessage("<PERSON><PERSON><PERSON> thuốc thành công!");
        fetchMedicines();
      } else {
        setMessage("Lỗi xoá thuốc!");
      }
    } catch (err) {
      setMessage("Lỗi xoá thuốc!");
      console.error(err);
    }
  };

  return (
    <div className="medicine">
      <div className="medicine__toolbar">
        <h2 className="medicine__title">Quản lý Thuốc</h2>
        <button
          className="medicine__add-btn"
          onClick={() => navigate("/medicine/add")}
          type="button"
        >
          + Thêm thuốc
        </button>
      </div>

      {message && <div className="medicine__message">{message}</div>}

      <table className="medicine__table">
        <thead>
          <tr>
            <th>#</th>
            <th>Tên thuốc</th>
            <th>Danh mục</th>
            <th>Giá</th>
            <th>Tồn kho</th>
            <th>Hết hạn</th>
            <th>Nhà sản xuất</th>
            <th>Hành động</th>
          </tr>
        </thead>
        <tbody>
          {medicines.map((med, index) => (
            <tr key={med.id}>
              <td>{index + 1}</td>
              <td>{med.name}</td>
              <td>{med.category?.name}</td>
              <td>{med.price.toLocaleString()}</td>
              <td>{med.stock}</td>
              <td>{med.expiration_date}</td>
              <td>{med.manufacturer}</td>
              <td>
                <button
                  className="medicine__action-btn medicine__action-btn--edit"
                  onClick={() => navigate(`/medicine/edit/${med.id}`)}
                  type="button"
                >
                  Sửa
                </button>
                <button
                  className="medicine__action-btn medicine__action-btn--delete"
                  onClick={() => handleDelete(med.id)}
                  type="button"
                >
                  Xoá
                </button>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default MedicineManager;

import React, { useEffect, useState } from "react";
import "bootstrap/dist/css/bootstrap.min.css";
import { getMedicine } from "../../services/medicine";
import { createPrescription } from "../../services/prescription";
import { getPatient } from "../../services/user";

const PrescriptionForm = () => {
  const [patient, setPatient] = useState([]);
  const [medicines, setMedicines] = useState([]);
  const [formData, setFormData] = useState({
    patient_id: "",
    notes: "",
    items: [],
  });
  const [newItem, setNewItem] = useState({
    id: "",
    quantity: "",
    frequency: "",
  });
  const [message, setMessage] = useState("");

  useEffect(() => {
    fetchPatients();
    fetchMedicines();
  }, []);

  const fetchPatients = async () => {
    const response = await getPatient();
    setPatient(response);
  };

  const fetchMedicines = async () => {
    const response = await getMedicine();
    setMedicines(response);
  };

  const handleChange = (e) => {
    setFormData((prev) => ({ ...prev, [e.target.name]: e.target.value }));
  };

  const handleItemChange = (e) => {
    setNewItem((prev) => ({ ...prev, [e.target.name]: e.target.value }));
  };

  const handleAddItem = () => {
    if (!newItem.id || !newItem.quantity || !newItem.frequency) {
      return setMessage("Vui lòng điền đầy đủ thông tin thuốc");
    }

    const existingItem = formData.items.find((item) => item.id === newItem.id);
    if (existingItem) {
      return setMessage("Thuốc đã tồn tại trong đơn thuốc");
    }

    setFormData((prev) => ({
      ...prev,
      items: [...prev.items, newItem],
    }));
    setNewItem({ id: "", quantity: "", frequency: "" });
    setMessage("");
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    const { patient_id, notes, items } = formData;
    if (!patient_id || items.length === 0) {
      return setMessage("Vui lòng điền đầy đủ thông tin đơn thuốc");
    }
    try {
      const response = await createPrescription({ patient_id, notes, items });
      if (response.status === 201) {
        setMessage("Đơn thuốc đã được tạo thành công!");
        setFormData({ patient_id: "", notes: "", items: [] });
      } else {
        setMessage("Có lỗi xảy ra khi tạo đơn thuốc");
      }
    } catch (error) {
      setMessage("Có lỗi xảy ra khi tạo đơn thuốc");
    }
  };

  return (
    <div className="container my-5">
      <h2 className="mb-4 text-primary">Tạo đơn thuốc mới</h2>

      {message && <div className="alert alert-info">{message}</div>}

      <form onSubmit={handleSubmit}>
        <div className="mb-3">
          <label className="form-label fw-semibold">Bệnh nhân</label>
          <select
            name="patient_id"
            className="form-select"
            value={formData.patient_id}
            onChange={handleChange}
          >
            <option value="">Chọn bệnh nhân</option>
            {patient &&
              patient.map((pat) => (
                <option
                  key={pat.id}
                  value={pat.id}
                >{`${pat.first_name} ${pat.last_name}`}</option>
              ))}
          </select>
        </div>

        <div className="mb-3">
          <label className="form-label fw-semibold">Ghi chú</label>
          <textarea
            name="notes"
            className="form-control"
            rows="3"
            value={formData.notes}
            onChange={handleChange}
          ></textarea>
        </div>

        <hr />
        <h5>Thêm thuốc vào đơn</h5>
        <div className="row mb-3">
          <div className="col-md-4">
            <select
              name="id"
              className="form-select"
              value={newItem.id}
              onChange={handleItemChange}
            >
              <option value="">Chọn thuốc</option>
              {medicines.map((med) => (
                <option key={med.id} value={med.id}>
                  {med.name}
                </option>
              ))}
            </select>
          </div>
          <div className="col-md-3">
            <input
              type="number"
              name="quantity"
              className="form-control"
              placeholder="Số lượng"
              value={newItem.quantity}
              onChange={handleItemChange}
            />
          </div>
          <div className="col-md-3">
            <input
              type="text"
              name="frequency"
              className="form-control"
              placeholder="Tần suất"
              value={newItem.frequency}
              onChange={handleItemChange}
            />
          </div>
          <div className="col-md-2">
            <button
              type="button"
              className="btn btn-success w-100"
              onClick={handleAddItem}
            >
              Thêm
            </button>
          </div>
        </div>

        <ul className="list-group mb-3">
          {formData.items.map((item, index) => (
            <li key={index} className="list-group-item">
              {`Thuốc: ${
                medicines.find((medi) => medi.id == item.id).name
              } - Số lượng: ${item.quantity} - Tần suất: ${item.frequency}`}
              <button
                type="button"
                className="btn btn-danger btn-sm float-end"
                onClick={() => {
                  setFormData((prev) => ({
                    ...prev,
                    items: prev.items.filter((_, i) => i !== index),
                  }));
                }}
              >
                Xóa
              </button>
            </li>
          ))}
        </ul>

        <button type="submit" className="btn btn-primary w-100">
          Tạo đơn thuốc
        </button>
      </form>
    </div>
  );
};

export default PrescriptionForm;

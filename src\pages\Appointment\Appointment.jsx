import React, { useEffect, useState } from "react";
import { getAppointment, updateAppointment } from "../../services/appointment";
import { useNavigate } from "react-router-dom";
import "./Appointment.css"; // Import your CSS styles
function AppointmentList() {
  const navigate = useNavigate();
  const [appointments, setAppointments] = useState([]);
  const [status, setStatus] = useState("PENDING");

  const user = JSON.parse(localStorage.getItem("user"));

  const fetchAppointments = async () => {
    try {
      const response = await getAppointment(status);
      setAppointments(response);
    } catch (error) {
      console.error("Error fetching appointments:", error);
    }
  };

  const handleCancel = async (id) => {
    if (window.confirm("Bạn có chắc muốn hủy cuộc hẹn này?")) {
      try {
        await updateAppointment(id, "CANCELLED");
        fetchAppointments();
      } catch (error) {
        console.error("Error cancelling appointment:", error);
      }
    }
  };

  const handleConfirm = async (id) => {
    if (window.confirm("Bạn có chắc muốn xác nhận cuộc hẹn này?")) {
      try {
        await updateAppointment(id, "CONFIRMED");
        fetchAppointments();
      } catch (error) {
        console.error("Error confirming appointment:", error);
      }
    }
  };

  const handleReject = async (id) => {
    if (window.confirm("Bạn có chắc muốn từ chối cuộc hẹn này?")) {
      try {
        await updateAppointment(id, "REJECTED");
        fetchAppointments();
      } catch (error) {
        console.error("Error rejecting appointment:", error);
      }
    }
  };

  useEffect(() => {
    fetchAppointments();
  }, [status]);

  const renderFilterButton = (label, st, color) => (
    <button
      className={`appointment__filter-btn appointment__filter-btn--${color} ${
        status === st ? "active" : ""
      }`}
      onClick={() => setStatus(st)}
      type="button"
    >
      {label}
    </button>
  );

  return (
    <div className="appointment">
      <h2 className="appointment__title">Lịch sử cuộc hẹn</h2>

      <div className="appointment__toolbar">
        <div className="appointment__filter-group">
          {renderFilterButton("Chờ xác nhận", "PENDING", "warning")}
          {renderFilterButton("Đã xác nhận", "CONFIRMED", "success")}
          {renderFilterButton("Đã từ chối", "REJECTED", "primary")}
          {renderFilterButton("Đã hủy", "CANCELLED", "danger")}
        </div>

        <button
          className="appointment__button"
          onClick={() =>
            navigate(
              user && user.role === "PATIENT"
                ? "/appointment/add"
                : "/available"
            )
          }
          type="button"
        >
          {user && user.role === "PATIENT" ? "Đặt lịch hẹn mới" : "Xem lịch"}
        </button>
      </div>

      {appointments.length === 0 ? (
        <p className="appointment__empty">Không có cuộc hẹn nào.</p>
      ) : (
        appointments.map((appointment) => (
          <div key={appointment.id} className="appointment__card">
            <div className="appointment__card-header">
              <h5 className="appointment__card-title">
                Cuộc hẹn #{appointment.id}
              </h5>
              <span
                className={`appointment__badge appointment__badge--${appointment.status.toLowerCase()}`}
              >
                {appointment.status}
              </span>
            </div>

            <p className="appointment__info">
              <strong>
                {user && user.role === "PATIENT" ? "Bác sĩ" : "Bệnh nhân"}:
              </strong>{" "}
              {user && user.role === "PATIENT"
                ? `${appointment.doctor.first_name} ${appointment.doctor.last_name}`
                : `${appointment.patient.first_name} ${appointment.patient.last_name}`}
            </p>
            <p className="appointment__info">
              <strong>Ngày hẹn:</strong>{" "}
              {new Date(
                appointment?.doctor_availability.date
              ).toLocaleDateString()}
            </p>
            <p className="appointment__info">
              <strong>Thời gian:</strong>{" "}
              {appointment?.doctor_availability.shift.start_time.substring(
                0,
                5
              )}{" "}
              -{" "}
              {appointment?.doctor_availability.shift.end_time.substring(0, 5)}{" "}
            </p>
            <p className="appointment__info">
              <strong>Ghi chú:</strong> {appointment.notes || "Không có"}
            </p>
            <p className="appointment__info">
              <strong>Ngày tạo:</strong>{" "}
              {new Date(appointment.created_at).toLocaleString()}
            </p>

            {appointment.status === "PENDING" ? (
              user && user.role === "PATIENT" ? (
                <button
                  className="appointment__action-btn appointment__action-btn--danger"
                  onClick={() => handleCancel(appointment.id)}
                  type="button"
                >
                  Hủy cuộc hẹn
                </button>
              ) : (
                <div className="appointment__action-group">
                  <button
                    className="appointment__action-btn appointment__action-btn--danger"
                    onClick={() => handleReject(appointment.id)}
                    type="button"
                  >
                    Từ chối
                  </button>

                  <button
                    className="appointment__action-btn appointment__action-btn--success"
                    onClick={() => handleConfirm(appointment.id)}
                    type="button"
                  >
                    Xác nhận
                  </button>
                </div>
              )
            ) : (
              ""
            )}
          </div>
        ))
      )}
    </div>
  );
}

export default AppointmentList;

import React from 'react';

const Home = () => {
  return (
    <div>
      
      {/* Banner */}
      <div className="bg-light text-dark text-center py-5">
        <div className="container">
          <h1 className="display-4">Chăm sóc sức khỏe toàn diện</h1>
          <p className="lead">Đặt lịch khám, tư vấn trực tuyến với bác sĩ chỉ trong vài phút.</p>
          <a href="#" className="btn btn-primary btn-lg">Đặt lịch ngay</a>
        </div>
      </div>

      {/* Giới thiệu */}
      <section className="py-5">
        <div className="container">
          <h2 className="text-center mb-4">Về chúng tôi</h2>
          <p className="text-center">HealthCare là nền tảng kết nối bệnh nhân với các bác sĩ và dịch vụ y tế chất lượng cao. Vớ<PERSON> hệ thống hiện đại, chúng tôi giúp bạn dễ dàng quản lý sức khỏe mọi lúc, mọi nơi.</p>
        </div>
      </section>

      {/* Dịch vụ */}
      <section className="bg-light py-5">
        <div className="container">
          <h2 className="text-center mb-4">Dịch vụ của chúng tôi</h2>
          <div className="row text-center">
            <div className="col-md-4 mb-4">
              <div className="card h-100">
                <div className="card-body">
                  <h5 className="card-title">Tư vấn online</h5>
                  <p className="card-text">Kết nối nhanh chóng với bác sĩ qua video hoặc chat.</p>
                </div>
              </div>
            </div>
            <div className="col-md-4 mb-4">
              <div className="card h-100">
                <div className="card-body">
                  <h5 className="card-title">Đặt lịch khám</h5>
                  <p className="card-text">Chọn bác sĩ, thời gian và địa điểm linh hoạt theo nhu cầu.</p>
                </div>
              </div>
            </div>
            <div className="col-md-4 mb-4">
              <div className="card h-100">
                <div className="card-body">
                  <h5 className="card-title">Quản lý hồ sơ</h5>
                  <p className="card-text">Theo dõi lịch sử khám bệnh và đơn thuốc mọi lúc, mọi nơi.</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Home;

import React, { useState } from "react";
import { createLab } from "../../services/lab";

export default function LabForm() {
  const [formData, setFormData] = useState({
    patient_id: "",
    type: "",
    description: "",
  });

  const [message, setMessage] = useState("");
  const [error, setError] = useState("");

  const handleChange = (e) => {
    setFormData({ ...formData, [e.target.name]: e.target.value });
    setMessage("");
    setError("");
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      const response = await createLab(formData);
      if (response.status === 201) {
        setMessage("Đặt xét nghiệm thành công!");
        setFormData({
          patient_id: "",
          type: "",
          description: "",
        });
      } else {
        setError("Lỗi khi đặt xét nghiệm. Vui lòng kiểm tra lại.");
      }
    } catch (err) {
      console.error(err);
      setError("Lỗi khi đặt xét nghiệm. Vui lòng kiểm tra lại.");
    }
  };

  return (
    <div className="container my-5">
      <div className="card shadow">
        <div className="card-header bg-primary text-white">
          <h4 className="mb-0">Đặt xét nghiệm</h4>
        </div>
        <div className="card-body">
          {message && <div className="alert alert-success">{message}</div>}
          {error && <div className="alert alert-danger">{error}</div>}

          <form onSubmit={handleSubmit}>
            <div className="mb-3">
              <label htmlFor="patient_id" className="form-label">
                Mã bệnh nhân
              </label>
              <input
                type="number"
                className="form-control"
                id="patient_id"
                name="patient_id"
                value={formData.patient_id}
                onChange={handleChange}
                required
              />
            </div>

            <div className="mb-3">
              <label htmlFor="type" className="form-label">
                Loại xét nghiệm
              </label>
              <input
                type="text"
                className="form-control"
                id="type"
                name="type"
                value={formData.type}
                onChange={handleChange}
                required
              />
            </div>

            <div className="mb-3">
              <label htmlFor="description" className="form-label">
                Mô tả
              </label>
              <textarea
                className="form-control"
                id="description"
                name="description"
                rows="3"
                value={formData.description}
                onChange={handleChange}
              ></textarea>
            </div>

            <button type="submit" className="btn btn-success px-4">
              Gửi yêu cầu
            </button>
          </form>
        </div>
      </div>
    </div>
  );
}

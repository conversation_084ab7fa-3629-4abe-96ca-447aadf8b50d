import React, { useState } from "react";
import { usePara<PERSON>, useNavigate } from "react-router-dom";
import { updateLab } from "../../services/lab";

export default function LabResult() {
  const { id } = useParams();
  const [result, setResult] = useState("");
  const navigate = useNavigate();

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      const response = await updateLab(id, { result });
      if (response.status === 200) {
        alert("✅ Cập nhật kết quả thành công!");
        navigate("/lab");
      } else {
        alert(" Cập nhật thất bại!");
      }
    } catch (err) {
      console.error(err);
      alert(" Có lỗi xảy ra khi cập nhật kết quả!");
    }
  };

  return (
    <div className="container my-5">
      <h4>Cập nhật kết quả cho xét nghiệm #{id}</h4>
      <form onSubmit={handleSubmit} className="mt-4">
        <div className="mb-3">
          <label className="form-label">Kết quả</label>
          <input
            type="text"
            className="form-control"
            value={result}
            onChange={(e) => setResult(e.target.value)}
            required
          />
        </div>
        <button type="submit" className="btn btn-primary me-2">
          Lưu
        </button>
        <button
          type="button"
          className="btn btn-secondary"
          onClick={() => navigate("/lab")}
        >
          Hủy
        </button>
      </form>
    </div>
  );
}
